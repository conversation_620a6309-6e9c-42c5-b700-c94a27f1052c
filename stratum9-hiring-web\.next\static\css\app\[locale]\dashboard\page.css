/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/react-loading-skeleton/dist/skeleton.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
@keyframes react-loading-skeleton {
  100% {
    transform: translateX(100%);
  }
}

.react-loading-skeleton {
  --base-color: #ebebeb;
  --highlight-color: #f5f5f5;
  --animation-duration: 1.5s;
  --animation-direction: normal;
  --pseudo-element-display: block; /* Enable animation */

  background-color: var(--base-color);

  width: 100%;
  border-radius: 0.25rem;
  display: inline-flex;
  line-height: 1;

  position: relative;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  overflow: hidden;
}

.react-loading-skeleton::after {
  content: ' ';
  display: var(--pseudo-element-display);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background-repeat: no-repeat;
  background-image: var(
    --custom-highlight-background,
    linear-gradient(
      90deg,
      var(--base-color) 0%,
      var(--highlight-color) 50%,
      var(--base-color) 100%
    )
  );
  transform: translateX(-100%);

  animation-name: react-loading-skeleton;
  animation-direction: var(--animation-direction);
  animation-duration: var(--animation-duration);
  animation-timing-function: ease-in-out;
  animation-iteration-count: infinite;
}

@media (prefers-reduced-motion) {
  .react-loading-skeleton {
    --pseudo-element-display: none; /* Disable animation */
  }
}

/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[11].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[11].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[11].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[11].use[5]!./src/styles/eventModal.scss ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContent {
  background: white;
  padding: 25px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
}

.modalHeaderWrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.modalHeaderWrapper h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
}

.closeButton {
  background: transparent;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  padding: 0 5px;
  line-height: 1;
  transition: color 0.2s;
}
.closeButton:hover {
  color: #333;
}

.modalBody {
  margin-bottom: 20px;
}
.modalBody p {
  margin: 0 0 10px;
  color: #555;
  line-height: 1.5;
}

.modalFooter {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
.modalFooter button {
  min-width: 100px;
}

.darkOutlineBtn {
  background-color: transparent;
  border: 1px solid #555;
  color: #333;
  padding: 10px 16px;
  font-weight: 500;
  transition: all 0.2s;
}
.darkOutlineBtn:hover {
  background-color: #f5f5f5;
}

.primaryBtn {
  background-color: #2563eb;
  border: none;
  color: white;
  padding: 10px 16px;
  font-weight: 600;
  transition: all 0.2s;
}
.primaryBtn:hover {
  background-color: #1d4ed8;
}

.rounded-md {
  border-radius: 4px;
}

.formGroup {
  margin-bottom: 16px;
}
.formGroup label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}
.formGroup input,
.formGroup select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.timeGroup {
  display: flex;
  gap: 16px;
}
.timeGroup .formGroup {
  flex: 1 1;
}

.timeInputContainer {
  display: flex;
  gap: 8px;
}
.timeInputContainer input {
  flex: 3 1;
}
.timeInputContainer select {
  flex: 1 1;
}

.buttonGroup {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}
.buttonGroup button {
  padding: 10px 20px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
}

.saveButton {
  background-color: #4285f4;
  color: white;
  border: none;
}

.cancelButton {
  background-color: transparent;
  border: 1px solid #ddd;
}
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[7].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[7].use[5]!./src/styles/commonPage.module.scss ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.commonPage_job_page__u5bZM .commonPage_skills_info_box__2AtNg ul, .commonPage_job_page__u5bZM .commonPage_skills_info_box__2AtNg .commonPage_skills_tab__gcN4O, .commonPage_dashboard_page__rPPSA .commonPage_dashboard_inner_head__ItdV2 .commonPage_header_tab__wTI4h {
  list-style: none;
  padding: 0;
  margin: 0;
}

.commonPage_dashboard__stats_header__Tnw8X {
  background: #fff;
  border-radius: 25px;
  padding: 2rem 2.5rem;
  margin-bottom: 28px;
  box-shadow: 0 2px 9px rgba(0, 0, 0, 0.0901960784);
  display: flex;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
}
.commonPage_dashboard__stats_header__Tnw8X .commonPage_dashboard__stats__2wQ5d {
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  z-index: 2;
}
.commonPage_dashboard__stats_header__Tnw8X .commonPage_dashboard__stats__2wQ5d .commonPage_dashboard__stat__Orr_8 {
  display: flex;
  flex-direction: column;
  position: relative;
  padding: 0 1.5rem;
}
.commonPage_dashboard__stats_header__Tnw8X .commonPage_dashboard__stats__2wQ5d .commonPage_dashboard__stat__Orr_8::after {
  content: "";
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 1px;
  background-color: rgba(51, 51, 51, 0.1);
  z-index: 1;
  margin: auto 0;
}
.commonPage_dashboard__stats_header__Tnw8X .commonPage_dashboard__stats__2wQ5d .commonPage_dashboard__stat__Orr_8:first-child {
  padding-left: 0;
}
.commonPage_dashboard__stats_header__Tnw8X .commonPage_dashboard__stats__2wQ5d .commonPage_dashboard__stat__Orr_8.commonPage_border_none__lzAhj {
  padding-right: 0;
}
.commonPage_dashboard__stats_header__Tnw8X .commonPage_dashboard__stats__2wQ5d .commonPage_dashboard__stat__Orr_8.commonPage_border_none__lzAhj::after {
  display: none;
}
.commonPage_dashboard__stats_header__Tnw8X .commonPage_dashboard__stats__2wQ5d .commonPage_dashboard__stat__Orr_8 .commonPage_dashboard__stat_label__Bkzql {
  color: #333;
  font-size: 1.6rem;
  font-weight: 700;
  padding-bottom: 1.2rem;
}
@media screen and (min-width: 1200px) and (max-width: 1399px) {
  .commonPage_dashboard__stats_header__Tnw8X .commonPage_dashboard__stats__2wQ5d .commonPage_dashboard__stat__Orr_8 .commonPage_dashboard__stat_label__Bkzql {
    font-size: 1.5rem;
  }
}
@media screen and (min-width: 992px) and (max-width: 1199px) {
  .commonPage_dashboard__stats_header__Tnw8X .commonPage_dashboard__stats__2wQ5d .commonPage_dashboard__stat__Orr_8 .commonPage_dashboard__stat_label__Bkzql {
    font-size: 1.5rem;
    max-width: 90px;
    min-height: 60px;
  }
}
.commonPage_dashboard__stats_header__Tnw8X .commonPage_dashboard__stats__2wQ5d .commonPage_dashboard__stat__Orr_8 .commonPage_dashboard__stat_value__8bFsP {
  font-size: 2rem;
  color: #436eb6;
  font-weight: 700;
}
.commonPage_dashboard__stats_header__Tnw8X .commonPage_dashboard__stats__2wQ5d .commonPage_dashboard__stat_design__EJo9c {
  position: relative;
  min-width: 135px;
}
.commonPage_dashboard__stats_header__Tnw8X .commonPage_dashboard__stat_image__G_EHf {
  width: 230px;
  min-width: 230px;
  height: 115px;
  object-fit: contain;
  position: absolute;
  z-index: 1;
  top: 0px;
  right: 0;
  object-position: right;
}
@media (max-width: 991px) {
  .commonPage_dashboard__stats_header__Tnw8X {
    flex-direction: column;
    padding: 2rem;
  }
  .commonPage_dashboard__stats_header__Tnw8X .commonPage_dashboard__stats__2wQ5d {
    flex-direction: column;
  }
  .commonPage_dashboard__stats_header__Tnw8X .commonPage_dashboard__stats__2wQ5d .commonPage_dashboard__stat__Orr_8 {
    padding: 0;
    flex-direction: row;
    justify-content: space-between;
    border-bottom: 1px solid rgba(51, 51, 51, 0.1);
    margin-bottom: 10px;
  }
  .commonPage_dashboard__stats_header__Tnw8X .commonPage_dashboard__stats__2wQ5d .commonPage_dashboard__stat__Orr_8:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }
  .commonPage_dashboard__stats_header__Tnw8X .commonPage_dashboard__stats__2wQ5d .commonPage_dashboard__stat__Orr_8::after {
    display: none;
  }
  .commonPage_dashboard__stats_header__Tnw8X .commonPage_dashboard__stat_image__G_EHf {
    display: none;
  }
}

.commonPage_job_page__u5bZM .commonPage_job_info__odPrH {
  border-radius: 8px;
  background: rgba(67, 110, 182, 0.1);
  padding: 5px 15px;
  font-size: 1.4rem;
  font-weight: 500;
  color: #333;
}
.commonPage_job_page__u5bZM .commonPage_job_info__odPrH.commonPage_text_xs__qrSkH {
  font-size: 1.3rem;
}
.commonPage_job_page__u5bZM .commonPage_job_info__odPrH a {
  color: #436eb6;
  text-decoration: underline !important;
  font-size: 1.4rem;
}
.commonPage_job_page__u5bZM .commonPage_section_heading__BLY4z {
  font-size: 1.6rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 16px;
}
.commonPage_job_page__u5bZM .commonPage_section_heading__BLY4z span {
  color: #436eb6;
}
.commonPage_job_page__u5bZM .commonPage_interview_form_icon__g_tEp {
  width: 100%;
  height: 260px;
  margin-top: -20px;
  padding: 0px 20px;
}
.commonPage_job_page__u5bZM .commonPage_inner_heading__RWTB8 {
  font-size: 2.4rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 20px;
}
.commonPage_job_page__u5bZM .commonPage_inner_heading__RWTB8 span {
  color: #cb9932;
}
.commonPage_job_page__u5bZM .commonPage_skills_info_box__2AtNg {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}
.commonPage_job_page__u5bZM .commonPage_skills_info_box__2AtNg ul {
  display: flex;
  gap: 4rem;
  align-items: center;
  margin-bottom: 0;
}
.commonPage_job_page__u5bZM .commonPage_skills_info_box__2AtNg ul li {
  font-size: 1.4rem;
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
  gap: 1rem;
}
.commonPage_job_page__u5bZM .commonPage_skills_info_box__2AtNg ul li span {
  width: 20px;
  height: 20px;
  border-radius: 100%;
  display: block;
}
.commonPage_job_page__u5bZM .commonPage_skills_info_box__2AtNg ul li span.commonPage_selecting__K9Whk {
  background: #cb9932;
}
.commonPage_job_page__u5bZM .commonPage_skills_info_box__2AtNg ul li span.commonPage_selected__qhYoX {
  background: #436eb6;
}
.commonPage_job_page__u5bZM .commonPage_skills_info_box__2AtNg ul li span.commonPage_selection__suLxn {
  background: rgba(67, 110, 182, 0.1);
}
.commonPage_job_page__u5bZM .commonPage_skills_info_box__2AtNg .commonPage_skills_tab__gcN4O {
  display: flex;
  align-items: center;
  border: 1px solid #333;
  border-radius: 14px;
  display: inline-flex;
  overflow: hidden;
}
.commonPage_job_page__u5bZM .commonPage_skills_info_box__2AtNg .commonPage_skills_tab__gcN4O li {
  font-size: 1.6rem;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  padding: 8px 30px;
  position: relative;
  margin: 0;
  text-align: center;
  min-width: 155px;
}
.commonPage_job_page__u5bZM .commonPage_skills_info_box__2AtNg .commonPage_skills_tab__gcN4O li.commonPage_active__ZAi6R {
  position: relative;
  color: #ffffff;
  background: #436eb6;
}
.commonPage_job_page__u5bZM .commonPage_career-skill-card__MBQ5U {
  min-height: 280px;
}

.commonPage_dashboard_page__rPPSA .commonPage_dashboard_inner_head__ItdV2 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 30px;
  margin-bottom: 20px;
}
.commonPage_dashboard_page__rPPSA .commonPage_dashboard_inner_head__ItdV2 .commonPage_header_tab__wTI4h {
  display: flex;
  align-items: center;
  border: 1px solid #333;
  border-radius: 14px;
  display: inline-flex;
  overflow: hidden;
}
.commonPage_dashboard_page__rPPSA .commonPage_dashboard_inner_head__ItdV2 .commonPage_header_tab__wTI4h li {
  font-size: 1.6rem;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  padding: 8px 30px;
  position: relative;
  margin: 0;
  text-align: center;
  min-width: 155px;
}
.commonPage_dashboard_page__rPPSA .commonPage_dashboard_inner_head__ItdV2 .commonPage_header_tab__wTI4h li.commonPage_active__ZAi6R {
  position: relative;
  color: #ffffff;
  background: #436eb6;
}
.commonPage_dashboard_page__rPPSA .commonPage_dashboard_inner_head__ItdV2 .commonPage_search_box__nvV9y {
  width: 35%;
}
@media (max-width: 767px) {
  .commonPage_dashboard_page__rPPSA .commonPage_dashboard_inner_head__ItdV2 {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    overflow: auto;
  }
  .commonPage_dashboard_page__rPPSA .commonPage_dashboard_inner_head__ItdV2 .commonPage_header_tab__wTI4h {
    border: none;
    border-radius: 14px;
    overflow: auto;
    width: 100%;
    border-radius: 0px;
    white-space: nowrap;
    gap: 15px;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none;
    padding: 0px 5px;
  }
  .commonPage_dashboard_page__rPPSA .commonPage_dashboard_inner_head__ItdV2 .commonPage_header_tab__wTI4h::-webkit-scrollbar {
    display: none;
  }
  .commonPage_dashboard_page__rPPSA .commonPage_dashboard_inner_head__ItdV2 .commonPage_header_tab__wTI4h li {
    font-size: 1.6rem;
    padding: 5px 0;
    padding-bottom: 10px;
    min-width: max-content;
    font-weight: 700;
    border-bottom: 2px solid transparent;
  }
  .commonPage_dashboard_page__rPPSA .commonPage_dashboard_inner_head__ItdV2 .commonPage_header_tab__wTI4h li.commonPage_active__ZAi6R {
    position: relative;
    color: #436eb6;
    background: transparent;
    border-bottom: 2px solid #436eb6;
  }
  .commonPage_dashboard_page__rPPSA .commonPage_dashboard_inner_head__ItdV2 .commonPage_search_box__nvV9y {
    width: 100%;
  }
}

.commonPage_resume_page__ObfJ4.commonPage_upload_resume_page__myQNw .commonPage_inner_page__OLzGY .commonPage_operation_admins_img__D0x2_ {
  width: 100%;
  height: auto;
  object-fit: contain;
}
.commonPage_resume_page__ObfJ4.commonPage_manual_upload_resume__zxaL5 .commonPage_inner_page__OLzGY .commonPage_input_type_file__FzGfA input {
  border: 1px solid rgba(255, 255, 255, 0.6);
  padding: 11px 15px;
  font-size: 1.4rem;
  border-radius: 12px;
  background: rgba(51, 51, 51, 0.05);
  color: #333;
  width: 100%;
}
.commonPage_resume_page__ObfJ4.commonPage_manual_upload_resume__zxaL5 .commonPage_inner_page__OLzGY .commonPage_candidate_card__CSBi9 {
  border-radius: 30px;
  border: 2px solid rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.05);
  padding: 20px;
  margin-bottom: 20px;
}
.commonPage_resume_page__ObfJ4.commonPage_manual_upload_resume__zxaL5 .commonPage_inner_page__OLzGY .commonPage_candidate_card__CSBi9 .commonPage_candidate_card_header__PHFje {
  margin-bottom: 15px;
}
.commonPage_resume_page__ObfJ4.commonPage_manual_upload_resume__zxaL5 .commonPage_inner_page__OLzGY .commonPage_candidate_card__CSBi9 .commonPage_candidate_card_header__PHFje h3 {
  color: #333;
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  margin: 0px;
}
.commonPage_resume_page__ObfJ4.commonPage_manual_upload_resume__zxaL5 .commonPage_inner_page__OLzGY .commonPage_add_another_candidate_link__Yn_PT {
  text-align: left;
}
.commonPage_resume_page__ObfJ4.commonPage_candidate_qualification_page__NA96P .commonPage_inner_page__OLzGY .commonPage_approved_status_indicator__v6ejj {
  display: flex;
  gap: 30px;
  margin-top: 10px;
}
.commonPage_resume_page__ObfJ4.commonPage_candidate_qualification_page__NA96P .commonPage_inner_page__OLzGY .commonPage_approved_status_indicator__v6ejj p {
  font-size: 1.4rem;
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
  gap: 5px;
}
.commonPage_resume_page__ObfJ4.commonPage_candidate_qualification_page__NA96P .commonPage_inner_page__OLzGY .commonPage_approved_status_indicator__v6ejj p span {
  background: rgba(229, 190, 83, 0.1);
  border: 1px solid #e5be53;
  width: 20px;
  min-width: 20px;
  height: 20px;
  border-radius: 50%;
}
.commonPage_resume_page__ObfJ4.commonPage_candidate_qualification_page__NA96P .commonPage_inner_page__OLzGY .commonPage_approved_status_indicator__v6ejj p:nth-child(2) span {
  background: rgba(208, 0, 0, 0.05);
  border: 1px solid rgba(208, 0, 0, 0.1);
}
.commonPage_resume_page__ObfJ4.commonPage_candidates_list_page__NEU5r .commonPage_candidates_list_section__6ZlUX {
  margin-bottom: 20px;
}
.commonPage_resume_page__ObfJ4.commonPage_candidates_list_page__NEU5r .commonPage_candidates_list_section__6ZlUX .commonPage_section_name__VK6fY {
  margin-bottom: 20px;
}
.commonPage_resume_page__ObfJ4.commonPage_candidates_list_page__NEU5r .commonPage_candidates_list_section__6ZlUX .commonPage_section_name__VK6fY h3 {
  font-size: 22px;
  font-weight: 700;
  color: #333;
  margin-bottom: 6px;
}
.commonPage_resume_page__ObfJ4.commonPage_candidates_list_page__NEU5r .commonPage_candidates_list_section__6ZlUX .commonPage_section_name__VK6fY p {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 0px;
}
