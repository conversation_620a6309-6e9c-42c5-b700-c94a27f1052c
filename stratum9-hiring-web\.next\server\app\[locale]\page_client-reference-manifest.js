globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/[locale]/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js":{"*":{"id":"(ssr)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/header/HeaderWrapper.tsx":{"*":{"id":"(ssr)/./src/components/header/HeaderWrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/redux/ReduxProvider.tsx":{"*":{"id":"(ssr)/./src/redux/ReduxProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/not-found.tsx":{"*":{"id":"(ssr)/./src/app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/views/profile/UserProfile.tsx":{"*":{"id":"(ssr)/./src/components/views/profile/UserProfile.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/home/<USER>":{"*":{"id":"(ssr)/./src/app/[locale]/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/calendar/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/calendar/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/views/activityLogs/ActivityLogs.tsx":{"*":{"id":"(ssr)/./src/components/views/activityLogs/ActivityLogs.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/buy-subscription/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/buy-subscription/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/views/dashboard/Dashboard.tsx":{"*":{"id":"(ssr)/./src/components/views/dashboard/Dashboard.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/views/jobRequirement/HiringType.tsx":{"*":{"id":"(ssr)/./src/components/views/jobRequirement/HiringType.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/views/jobRequirement/Generatejob.tsx":{"*":{"id":"(ssr)/./src/components/views/jobRequirement/Generatejob.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/career-based-skills/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/career-based-skills/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/role-based-skills/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/role-based-skills/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/culture-based-skills/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/culture-based-skills/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/job-editor/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/job-editor/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/active-jobs/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/active-jobs/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/login/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/login/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\bootstrap\\dist\\css\\bootstrap.css":{"id":"(app-pages-browser)/./node_modules/bootstrap/dist/css/bootstrap.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\next-intl\\dist\\esm\\development\\shared\\NextIntlClientProvider.js":{"id":"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\react-hot-toast\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\src\\components\\header\\HeaderWrapper.tsx":{"id":"(app-pages-browser)/./src/components/header/HeaderWrapper.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\src\\redux\\ReduxProvider.tsx":{"id":"(app-pages-browser)/./src/redux/ReduxProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\src\\styles\\style.scss":{"id":"(app-pages-browser)/./src/styles/style.scss","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/[locale]/loading","static/chunks/app/%5Blocale%5D/loading.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/[locale]/loading","static/chunks/app/%5Blocale%5D/loading.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\src\\app\\not-found.tsx":{"id":"(app-pages-browser)/./src/app/not-found.tsx","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\src\\components\\views\\profile\\UserProfile.tsx":{"id":"(app-pages-browser)/./src/components/views/profile/UserProfile.tsx","name":"*","chunks":[],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\next\\dist\\client\\components\\builtin\\global-error.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\next\\dist\\esm\\client\\components\\builtin\\global-error.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\next\\dist\\lib\\metadata\\generate\\icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\next\\dist\\esm\\lib\\metadata\\generate\\icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\next\\dist\\next-devtools\\userspace\\app\\segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\node_modules\\next\\dist\\esm\\next-devtools\\userspace\\app\\segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\src\\app\\[locale]\\home\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/home/<USER>","name":"*","chunks":["app/[locale]/page","static/chunks/app/%5Blocale%5D/page.js"],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\src\\app\\[locale]\\calendar\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/calendar/page.tsx","name":"*","chunks":[],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\src\\components\\views\\activityLogs\\ActivityLogs.tsx":{"id":"(app-pages-browser)/./src/components/views/activityLogs/ActivityLogs.tsx","name":"*","chunks":[],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\src\\app\\[locale]\\buy-subscription\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/buy-subscription/page.tsx","name":"*","chunks":[],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\src\\components\\views\\dashboard\\Dashboard.tsx":{"id":"(app-pages-browser)/./src/components/views/dashboard/Dashboard.tsx","name":"*","chunks":[],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\src\\components\\views\\jobRequirement\\HiringType.tsx":{"id":"(app-pages-browser)/./src/components/views/jobRequirement/HiringType.tsx","name":"*","chunks":[],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\src\\components\\views\\jobRequirement\\Generatejob.tsx":{"id":"(app-pages-browser)/./src/components/views/jobRequirement/Generatejob.tsx","name":"*","chunks":[],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\src\\app\\[locale]\\career-based-skills\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/career-based-skills/page.tsx","name":"*","chunks":[],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\src\\app\\[locale]\\role-based-skills\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/role-based-skills/page.tsx","name":"*","chunks":[],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\src\\app\\[locale]\\culture-based-skills\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/culture-based-skills/page.tsx","name":"*","chunks":[],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\src\\app\\[locale]\\job-editor\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/job-editor/page.tsx","name":"*","chunks":[],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\src\\app\\[locale]\\active-jobs\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/active-jobs/page.tsx","name":"*","chunks":[],"async":false},"D:\\stratum 9\\stratum9-hiring-web\\src\\app\\[locale]\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/login/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\stratum 9\\stratum9-hiring-web\\src\\":[],"D:\\stratum 9\\stratum9-hiring-web\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"D:\\stratum 9\\stratum9-hiring-web\\src\\app\\loading":[],"D:\\stratum 9\\stratum9-hiring-web\\src\\app\\not-found":[],"D:\\stratum 9\\stratum9-hiring-web\\src\\app\\[locale]\\layout":[],"D:\\stratum 9\\stratum9-hiring-web\\src\\app\\[locale]\\loading":[],"D:\\stratum 9\\stratum9-hiring-web\\src\\app\\[locale]\\page":[{"inlined":false,"path":"static/css/app/[locale]/page.css"}]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/bootstrap/dist/css/bootstrap.css":{"*":{"id":"(rsc)/./node_modules/bootstrap/dist/css/bootstrap.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js":{"*":{"id":"(rsc)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/header/HeaderWrapper.tsx":{"*":{"id":"(rsc)/./src/components/header/HeaderWrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/redux/ReduxProvider.tsx":{"*":{"id":"(rsc)/./src/redux/ReduxProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/styles/style.scss":{"*":{"id":"(rsc)/./src/styles/style.scss","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/not-found.tsx":{"*":{"id":"(rsc)/./src/app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/views/profile/UserProfile.tsx":{"*":{"id":"(rsc)/./src/components/views/profile/UserProfile.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/home/<USER>":{"*":{"id":"(rsc)/./src/app/[locale]/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/calendar/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/calendar/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/views/activityLogs/ActivityLogs.tsx":{"*":{"id":"(rsc)/./src/components/views/activityLogs/ActivityLogs.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/buy-subscription/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/buy-subscription/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/views/dashboard/Dashboard.tsx":{"*":{"id":"(rsc)/./src/components/views/dashboard/Dashboard.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/views/jobRequirement/HiringType.tsx":{"*":{"id":"(rsc)/./src/components/views/jobRequirement/HiringType.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/views/jobRequirement/Generatejob.tsx":{"*":{"id":"(rsc)/./src/components/views/jobRequirement/Generatejob.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/career-based-skills/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/career-based-skills/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/role-based-skills/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/role-based-skills/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/culture-based-skills/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/culture-based-skills/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/job-editor/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/job-editor/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/active-jobs/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/active-jobs/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/login/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/login/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}