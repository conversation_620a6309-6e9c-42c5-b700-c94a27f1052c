"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/canvg";
exports.ids = ["vendor-chunks/canvg"];
exports.modules = {

/***/ "(ssr)/./node_modules/canvg/lib/index.es.js":
/*!********************************************!*\
  !*** ./node_modules/canvg/lib/index.es.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AElement: () => (/* binding */ AElement),\n/* harmony export */   AnimateColorElement: () => (/* binding */ AnimateColorElement),\n/* harmony export */   AnimateElement: () => (/* binding */ AnimateElement),\n/* harmony export */   AnimateTransformElement: () => (/* binding */ AnimateTransformElement),\n/* harmony export */   BoundingBox: () => (/* binding */ BoundingBox),\n/* harmony export */   CB1: () => (/* binding */ CB1),\n/* harmony export */   CB2: () => (/* binding */ CB2),\n/* harmony export */   CB3: () => (/* binding */ CB3),\n/* harmony export */   CB4: () => (/* binding */ CB4),\n/* harmony export */   Canvg: () => (/* binding */ Canvg),\n/* harmony export */   CircleElement: () => (/* binding */ CircleElement),\n/* harmony export */   ClipPathElement: () => (/* binding */ ClipPathElement),\n/* harmony export */   DefsElement: () => (/* binding */ DefsElement),\n/* harmony export */   DescElement: () => (/* binding */ DescElement),\n/* harmony export */   Document: () => (/* binding */ Document),\n/* harmony export */   Element: () => (/* binding */ Element),\n/* harmony export */   EllipseElement: () => (/* binding */ EllipseElement),\n/* harmony export */   FeColorMatrixElement: () => (/* binding */ FeColorMatrixElement),\n/* harmony export */   FeCompositeElement: () => (/* binding */ FeCompositeElement),\n/* harmony export */   FeDropShadowElement: () => (/* binding */ FeDropShadowElement),\n/* harmony export */   FeGaussianBlurElement: () => (/* binding */ FeGaussianBlurElement),\n/* harmony export */   FeMorphologyElement: () => (/* binding */ FeMorphologyElement),\n/* harmony export */   FilterElement: () => (/* binding */ FilterElement),\n/* harmony export */   Font: () => (/* binding */ Font),\n/* harmony export */   FontElement: () => (/* binding */ FontElement),\n/* harmony export */   FontFaceElement: () => (/* binding */ FontFaceElement),\n/* harmony export */   GElement: () => (/* binding */ GElement),\n/* harmony export */   GlyphElement: () => (/* binding */ GlyphElement),\n/* harmony export */   GradientElement: () => (/* binding */ GradientElement),\n/* harmony export */   ImageElement: () => (/* binding */ ImageElement),\n/* harmony export */   LineElement: () => (/* binding */ LineElement),\n/* harmony export */   LinearGradientElement: () => (/* binding */ LinearGradientElement),\n/* harmony export */   MarkerElement: () => (/* binding */ MarkerElement),\n/* harmony export */   MaskElement: () => (/* binding */ MaskElement),\n/* harmony export */   Matrix: () => (/* binding */ Matrix),\n/* harmony export */   MissingGlyphElement: () => (/* binding */ MissingGlyphElement),\n/* harmony export */   Mouse: () => (/* binding */ Mouse),\n/* harmony export */   PSEUDO_ZERO: () => (/* binding */ PSEUDO_ZERO),\n/* harmony export */   Parser: () => (/* binding */ Parser),\n/* harmony export */   PathElement: () => (/* binding */ PathElement),\n/* harmony export */   PathParser: () => (/* binding */ PathParser),\n/* harmony export */   PatternElement: () => (/* binding */ PatternElement),\n/* harmony export */   Point: () => (/* binding */ Point),\n/* harmony export */   PolygonElement: () => (/* binding */ PolygonElement),\n/* harmony export */   PolylineElement: () => (/* binding */ PolylineElement),\n/* harmony export */   Property: () => (/* binding */ Property),\n/* harmony export */   QB1: () => (/* binding */ QB1),\n/* harmony export */   QB2: () => (/* binding */ QB2),\n/* harmony export */   QB3: () => (/* binding */ QB3),\n/* harmony export */   RadialGradientElement: () => (/* binding */ RadialGradientElement),\n/* harmony export */   RectElement: () => (/* binding */ RectElement),\n/* harmony export */   RenderedElement: () => (/* binding */ RenderedElement),\n/* harmony export */   Rotate: () => (/* binding */ Rotate),\n/* harmony export */   SVGElement: () => (/* binding */ SVGElement),\n/* harmony export */   SVGFontLoader: () => (/* binding */ SVGFontLoader),\n/* harmony export */   Scale: () => (/* binding */ Scale),\n/* harmony export */   Screen: () => (/* binding */ Screen),\n/* harmony export */   Skew: () => (/* binding */ Skew),\n/* harmony export */   SkewX: () => (/* binding */ SkewX),\n/* harmony export */   SkewY: () => (/* binding */ SkewY),\n/* harmony export */   StopElement: () => (/* binding */ StopElement),\n/* harmony export */   StyleElement: () => (/* binding */ StyleElement),\n/* harmony export */   SymbolElement: () => (/* binding */ SymbolElement),\n/* harmony export */   TRefElement: () => (/* binding */ TRefElement),\n/* harmony export */   TSpanElement: () => (/* binding */ TSpanElement),\n/* harmony export */   TextElement: () => (/* binding */ TextElement),\n/* harmony export */   TextPathElement: () => (/* binding */ TextPathElement),\n/* harmony export */   TitleElement: () => (/* binding */ TitleElement),\n/* harmony export */   Transform: () => (/* binding */ Transform),\n/* harmony export */   Translate: () => (/* binding */ Translate),\n/* harmony export */   UnknownElement: () => (/* binding */ UnknownElement),\n/* harmony export */   UseElement: () => (/* binding */ UseElement),\n/* harmony export */   ViewPort: () => (/* binding */ ViewPort),\n/* harmony export */   compressSpaces: () => (/* binding */ compressSpaces),\n/* harmony export */   \"default\": () => (/* binding */ Canvg),\n/* harmony export */   getSelectorSpecificity: () => (/* binding */ getSelectorSpecificity),\n/* harmony export */   normalizeAttributeName: () => (/* binding */ normalizeAttributeName),\n/* harmony export */   normalizeColor: () => (/* binding */ normalizeColor),\n/* harmony export */   parseExternalUrl: () => (/* binding */ parseExternalUrl),\n/* harmony export */   presets: () => (/* binding */ index),\n/* harmony export */   toNumbers: () => (/* binding */ toNumbers),\n/* harmony export */   trimLeft: () => (/* binding */ trimLeft),\n/* harmony export */   trimRight: () => (/* binding */ trimRight),\n/* harmony export */   vectorMagnitude: () => (/* binding */ vectorMagnitude),\n/* harmony export */   vectorsAngle: () => (/* binding */ vectorsAngle),\n/* harmony export */   vectorsRatio: () => (/* binding */ vectorsRatio)\n/* harmony export */ });\n/* harmony import */ var core_js_modules_es_promise_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.promise.js */ \"(ssr)/./node_modules/core-js/modules/es.promise.js\");\n/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ \"(ssr)/./node_modules/@babel/runtime/helpers/asyncToGenerator.js\");\n/* harmony import */ var core_js_modules_es_string_match_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.string.match.js */ \"(ssr)/./node_modules/core-js/modules/es.string.match.js\");\n/* harmony import */ var core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.string.replace.js */ \"(ssr)/./node_modules/core-js/modules/es.string.replace.js\");\n/* harmony import */ var core_js_modules_es_string_starts_with_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.string.starts-with.js */ \"(ssr)/./node_modules/core-js/modules/es.string.starts-with.js\");\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.array.iterator.js */ \"(ssr)/./node_modules/core-js/modules/es.array.iterator.js\");\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */ \"(ssr)/./node_modules/core-js/modules/web.dom-collections.iterator.js\");\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/defineProperty.js\");\n/* harmony import */ var core_js_modules_es_array_reduce_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! core-js/modules/es.array.reduce.js */ \"(ssr)/./node_modules/core-js/modules/es.array.reduce.js\");\n/* harmony import */ var core_js_modules_es_string_ends_with_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! core-js/modules/es.string.ends-with.js */ \"(ssr)/./node_modules/core-js/modules/es.string.ends-with.js\");\n/* harmony import */ var core_js_modules_es_string_split_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! core-js/modules/es.string.split.js */ \"(ssr)/./node_modules/core-js/modules/es.string.split.js\");\n/* harmony import */ var raf__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! raf */ \"(ssr)/./node_modules/raf/index.js\");\n/* harmony import */ var core_js_modules_es_string_trim_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! core-js/modules/es.string.trim.js */ \"(ssr)/./node_modules/core-js/modules/es.string.trim.js\");\n/* harmony import */ var rgbcolor__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! rgbcolor */ \"(ssr)/./node_modules/rgbcolor/index.js\");\n/* harmony import */ var core_js_modules_es_array_index_of_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! core-js/modules/es.array.index-of.js */ \"(ssr)/./node_modules/core-js/modules/es.array.index-of.js\");\n/* harmony import */ var core_js_modules_es_string_includes_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! core-js/modules/es.string.includes.js */ \"(ssr)/./node_modules/core-js/modules/es.string.includes.js\");\n/* harmony import */ var core_js_modules_es_array_reverse_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! core-js/modules/es.array.reverse.js */ \"(ssr)/./node_modules/core-js/modules/es.array.reverse.js\");\n/* harmony import */ var svg_pathdata__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! svg-pathdata */ \"(ssr)/./node_modules/svg-pathdata/lib/SVGPathData.module.js\");\n/* harmony import */ var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! core-js/modules/es.regexp.to-string.js */ \"(ssr)/./node_modules/core-js/modules/es.regexp.to-string.js\");\n/* harmony import */ var stackblur_canvas__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! stackblur-canvas */ \"(ssr)/./node_modules/stackblur-canvas/dist/stackblur-es.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\r\n * Options preset for `OffscreenCanvas`.\r\n * @param config - Preset requirements.\r\n * @param config.DOMParser - XML/HTML parser from string into DOM Document.\r\n * @returns Preset object.\r\n */\nfunction offscreen() {\n  var {\n    DOMParser: DOMParserFallback\n  } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var preset = {\n    window: null,\n    ignoreAnimation: true,\n    ignoreMouse: true,\n    DOMParser: DOMParserFallback,\n\n    createCanvas(width, height) {\n      return new OffscreenCanvas(width, height);\n    },\n\n    createImage(url) {\n      return _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* () {\n        var response = yield fetch(url);\n        var blob = yield response.blob();\n        var img = yield createImageBitmap(blob);\n        return img;\n      })();\n    }\n\n  };\n\n  if (typeof DOMParser !== 'undefined' || typeof DOMParserFallback === 'undefined') {\n    Reflect.deleteProperty(preset, 'DOMParser');\n  }\n\n  return preset;\n}\n\n/**\r\n * Options preset for `node-canvas`.\r\n * @param config - Preset requirements.\r\n * @param config.DOMParser - XML/HTML parser from string into DOM Document.\r\n * @param config.canvas - `node-canvas` exports.\r\n * @param config.fetch - WHATWG-compatible `fetch` function.\r\n * @returns Preset object.\r\n */\nfunction node(_ref) {\n  var {\n    DOMParser,\n    canvas,\n    fetch\n  } = _ref;\n  return {\n    window: null,\n    ignoreAnimation: true,\n    ignoreMouse: true,\n    DOMParser,\n    fetch,\n    createCanvas: canvas.createCanvas,\n    createImage: canvas.loadImage\n  };\n}\n\nvar index = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\toffscreen: offscreen,\n\tnode: node\n});\n\n/**\r\n * HTML-safe compress white-spaces.\r\n * @param str - String to compress.\r\n * @returns String.\r\n */\nfunction compressSpaces(str) {\n  return str.replace(/(?!\\u3000)\\s+/gm, ' ');\n}\n/**\r\n * HTML-safe left trim.\r\n * @param str - String to trim.\r\n * @returns String.\r\n */\n\nfunction trimLeft(str) {\n  return str.replace(/^[\\n \\t]+/, '');\n}\n/**\r\n * HTML-safe right trim.\r\n * @param str - String to trim.\r\n * @returns String.\r\n */\n\nfunction trimRight(str) {\n  return str.replace(/[\\n \\t]+$/, '');\n}\n/**\r\n * String to numbers array.\r\n * @param str - Numbers string.\r\n * @returns Numbers array.\r\n */\n\nfunction toNumbers(str) {\n  var matches = (str || '').match(/-?(\\d+(?:\\.\\d*(?:[eE][+-]?\\d+)?)?|\\.\\d+)(?=\\D|$)/gm) || [];\n  return matches.map(parseFloat);\n} // Microsoft Edge fix\n\nvar allUppercase = /^[A-Z-]+$/;\n/**\r\n * Normalize attribute name.\r\n * @param name - Attribute name.\r\n * @returns Normalized attribute name.\r\n */\n\nfunction normalizeAttributeName(name) {\n  if (allUppercase.test(name)) {\n    return name.toLowerCase();\n  }\n\n  return name;\n}\n/**\r\n * Parse external URL.\r\n * @param url - CSS url string.\r\n * @returns Parsed URL.\r\n */\n\nfunction parseExternalUrl(url) {\n  //                      single quotes [2]\n  //                      v         double quotes [3]\n  //                      v         v         no quotes [4]\n  //                      v         v         v\n  var urlMatch = /url\\(('([^']+)'|\"([^\"]+)\"|([^'\")]+))\\)/.exec(url) || [];\n  return urlMatch[2] || urlMatch[3] || urlMatch[4];\n}\n/**\r\n * Transform floats to integers in rgb colors.\r\n * @param color - Color to normalize.\r\n * @returns Normalized color.\r\n */\n\nfunction normalizeColor(color) {\n  if (!color.startsWith('rgb')) {\n    return color;\n  }\n\n  var rgbParts = 3;\n  var normalizedColor = color.replace(/\\d+(\\.\\d+)?/g, (num, isFloat) => rgbParts-- && isFloat ? String(Math.round(parseFloat(num))) : num);\n  return normalizedColor;\n}\n\n// slightly modified version of https://github.com/keeganstreet/specificity/blob/master/specificity.js\nvar attributeRegex = /(\\[[^\\]]+\\])/g;\nvar idRegex = /(#[^\\s+>~.[:]+)/g;\nvar classRegex = /(\\.[^\\s+>~.[:]+)/g;\nvar pseudoElementRegex = /(::[^\\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi;\nvar pseudoClassWithBracketsRegex = /(:[\\w-]+\\([^)]*\\))/gi;\nvar pseudoClassRegex = /(:[^\\s+>~.[:]+)/g;\nvar elementRegex = /([^\\s+>~.[:]+)/g;\n\nfunction findSelectorMatch(selector, regex) {\n  var matches = regex.exec(selector);\n\n  if (!matches) {\n    return [selector, 0];\n  }\n\n  return [selector.replace(regex, ' '), matches.length];\n}\n/**\r\n * Measure selector specificity.\r\n * @param selector - Selector to measure.\r\n * @returns Specificity.\r\n */\n\n\nfunction getSelectorSpecificity(selector) {\n  var specificity = [0, 0, 0];\n  var currentSelector = selector.replace(/:not\\(([^)]*)\\)/g, '     $1 ').replace(/{[\\s\\S]*/gm, ' ');\n  var delta = 0;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, attributeRegex);\n  specificity[1] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, idRegex);\n  specificity[0] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, classRegex);\n  specificity[1] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, pseudoElementRegex);\n  specificity[2] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, pseudoClassWithBracketsRegex);\n  specificity[1] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, pseudoClassRegex);\n  specificity[1] += delta;\n  currentSelector = currentSelector.replace(/[*\\s+>~]/g, ' ').replace(/[#.]/g, ' ');\n  [currentSelector, delta] = findSelectorMatch(currentSelector, elementRegex); // lgtm [js/useless-assignment-to-local]\n\n  specificity[2] += delta;\n  return specificity.join('');\n}\n\nvar PSEUDO_ZERO = .00000001;\n/**\r\n * Vector magnitude.\r\n * @param v\r\n * @returns Number result.\r\n */\n\nfunction vectorMagnitude(v) {\n  return Math.sqrt(Math.pow(v[0], 2) + Math.pow(v[1], 2));\n}\n/**\r\n * Ratio between two vectors.\r\n * @param u\r\n * @param v\r\n * @returns Number result.\r\n */\n\nfunction vectorsRatio(u, v) {\n  return (u[0] * v[0] + u[1] * v[1]) / (vectorMagnitude(u) * vectorMagnitude(v));\n}\n/**\r\n * Angle between two vectors.\r\n * @param u\r\n * @param v\r\n * @returns Number result.\r\n */\n\nfunction vectorsAngle(u, v) {\n  return (u[0] * v[1] < u[1] * v[0] ? -1 : 1) * Math.acos(vectorsRatio(u, v));\n}\nfunction CB1(t) {\n  return t * t * t;\n}\nfunction CB2(t) {\n  return 3 * t * t * (1 - t);\n}\nfunction CB3(t) {\n  return 3 * t * (1 - t) * (1 - t);\n}\nfunction CB4(t) {\n  return (1 - t) * (1 - t) * (1 - t);\n}\nfunction QB1(t) {\n  return t * t;\n}\nfunction QB2(t) {\n  return 2 * t * (1 - t);\n}\nfunction QB3(t) {\n  return (1 - t) * (1 - t);\n}\n\nclass Property {\n  constructor(document, name, value) {\n    this.document = document;\n    this.name = name;\n    this.value = value;\n    this.isNormalizedColor = false;\n  }\n\n  static empty(document) {\n    return new Property(document, 'EMPTY', '');\n  }\n\n  split() {\n    var separator = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ' ';\n    var {\n      document,\n      name\n    } = this;\n    return compressSpaces(this.getString()).trim().split(separator).map(value => new Property(document, name, value));\n  }\n\n  hasValue(zeroIsValue) {\n    var {\n      value\n    } = this;\n    return value !== null && value !== '' && (zeroIsValue || value !== 0) && typeof value !== 'undefined';\n  }\n\n  isString(regexp) {\n    var {\n      value\n    } = this;\n    var result = typeof value === 'string';\n\n    if (!result || !regexp) {\n      return result;\n    }\n\n    return regexp.test(value);\n  }\n\n  isUrlDefinition() {\n    return this.isString(/^url\\(/);\n  }\n\n  isPixels() {\n    if (!this.hasValue()) {\n      return false;\n    }\n\n    var asString = this.getString();\n\n    switch (true) {\n      case asString.endsWith('px'):\n      case /^[0-9]+$/.test(asString):\n        return true;\n\n      default:\n        return false;\n    }\n  }\n\n  setValue(value) {\n    this.value = value;\n    return this;\n  }\n\n  getValue(def) {\n    if (typeof def === 'undefined' || this.hasValue()) {\n      return this.value;\n    }\n\n    return def;\n  }\n\n  getNumber(def) {\n    if (!this.hasValue()) {\n      if (typeof def === 'undefined') {\n        return 0;\n      }\n\n      return parseFloat(def);\n    }\n\n    var {\n      value\n    } = this;\n    var n = parseFloat(value);\n\n    if (this.isString(/%$/)) {\n      n /= 100.0;\n    }\n\n    return n;\n  }\n\n  getString(def) {\n    if (typeof def === 'undefined' || this.hasValue()) {\n      return typeof this.value === 'undefined' ? '' : String(this.value);\n    }\n\n    return String(def);\n  }\n\n  getColor(def) {\n    var color = this.getString(def);\n\n    if (this.isNormalizedColor) {\n      return color;\n    }\n\n    this.isNormalizedColor = true;\n    color = normalizeColor(color);\n    this.value = color;\n    return color;\n  }\n\n  getDpi() {\n    return 96.0; // TODO: compute?\n  }\n\n  getRem() {\n    return this.document.rootEmSize;\n  }\n\n  getEm() {\n    return this.document.emSize;\n  }\n\n  getUnits() {\n    return this.getString().replace(/[0-9.-]/g, '');\n  }\n\n  getPixels(axisOrIsFontSize) {\n    var processPercent = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n    if (!this.hasValue()) {\n      return 0;\n    }\n\n    var [axis, isFontSize] = typeof axisOrIsFontSize === 'boolean' ? [undefined, axisOrIsFontSize] : [axisOrIsFontSize];\n    var {\n      viewPort\n    } = this.document.screen;\n\n    switch (true) {\n      case this.isString(/vmin$/):\n        return this.getNumber() / 100.0 * Math.min(viewPort.computeSize('x'), viewPort.computeSize('y'));\n\n      case this.isString(/vmax$/):\n        return this.getNumber() / 100.0 * Math.max(viewPort.computeSize('x'), viewPort.computeSize('y'));\n\n      case this.isString(/vw$/):\n        return this.getNumber() / 100.0 * viewPort.computeSize('x');\n\n      case this.isString(/vh$/):\n        return this.getNumber() / 100.0 * viewPort.computeSize('y');\n\n      case this.isString(/rem$/):\n        return this.getNumber() * this.getRem();\n\n      case this.isString(/em$/):\n        return this.getNumber() * this.getEm();\n\n      case this.isString(/ex$/):\n        return this.getNumber() * this.getEm() / 2.0;\n\n      case this.isString(/px$/):\n        return this.getNumber();\n\n      case this.isString(/pt$/):\n        return this.getNumber() * this.getDpi() * (1.0 / 72.0);\n\n      case this.isString(/pc$/):\n        return this.getNumber() * 15;\n\n      case this.isString(/cm$/):\n        return this.getNumber() * this.getDpi() / 2.54;\n\n      case this.isString(/mm$/):\n        return this.getNumber() * this.getDpi() / 25.4;\n\n      case this.isString(/in$/):\n        return this.getNumber() * this.getDpi();\n\n      case this.isString(/%$/) && isFontSize:\n        return this.getNumber() * this.getEm();\n\n      case this.isString(/%$/):\n        return this.getNumber() * viewPort.computeSize(axis);\n\n      default:\n        {\n          var n = this.getNumber();\n\n          if (processPercent && n < 1.0) {\n            return n * viewPort.computeSize(axis);\n          }\n\n          return n;\n        }\n    }\n  }\n\n  getMilliseconds() {\n    if (!this.hasValue()) {\n      return 0;\n    }\n\n    if (this.isString(/ms$/)) {\n      return this.getNumber();\n    }\n\n    return this.getNumber() * 1000;\n  }\n\n  getRadians() {\n    if (!this.hasValue()) {\n      return 0;\n    }\n\n    switch (true) {\n      case this.isString(/deg$/):\n        return this.getNumber() * (Math.PI / 180.0);\n\n      case this.isString(/grad$/):\n        return this.getNumber() * (Math.PI / 200.0);\n\n      case this.isString(/rad$/):\n        return this.getNumber();\n\n      default:\n        return this.getNumber() * (Math.PI / 180.0);\n    }\n  }\n\n  getDefinition() {\n    var asString = this.getString();\n    var name = /#([^)'\"]+)/.exec(asString);\n\n    if (name) {\n      name = name[1];\n    }\n\n    if (!name) {\n      name = asString;\n    }\n\n    return this.document.definitions[name];\n  }\n\n  getFillStyleDefinition(element, opacity) {\n    var def = this.getDefinition();\n\n    if (!def) {\n      return null;\n    } // gradient\n\n\n    if (typeof def.createGradient === 'function') {\n      return def.createGradient(this.document.ctx, element, opacity);\n    } // pattern\n\n\n    if (typeof def.createPattern === 'function') {\n      if (def.getHrefAttribute().hasValue()) {\n        var patternTransform = def.getAttribute('patternTransform');\n        def = def.getHrefAttribute().getDefinition();\n\n        if (patternTransform.hasValue()) {\n          def.getAttribute('patternTransform', true).setValue(patternTransform.value);\n        }\n      }\n\n      return def.createPattern(this.document.ctx, element, opacity);\n    }\n\n    return null;\n  }\n\n  getTextBaseline() {\n    if (!this.hasValue()) {\n      return null;\n    }\n\n    return Property.textBaselineMapping[this.getString()];\n  }\n\n  addOpacity(opacity) {\n    var value = this.getColor();\n    var len = value.length;\n    var commas = 0; // Simulate old RGBColor version, which can't parse rgba.\n\n    for (var i = 0; i < len; i++) {\n      if (value[i] === ',') {\n        commas++;\n      }\n\n      if (commas === 3) {\n        break;\n      }\n    }\n\n    if (opacity.hasValue() && this.isString() && commas !== 3) {\n      var color = new rgbcolor__WEBPACK_IMPORTED_MODULE_13__(value);\n\n      if (color.ok) {\n        color.alpha = opacity.getNumber();\n        value = color.toRGBA();\n      }\n    }\n\n    return new Property(this.document, this.name, value);\n  }\n\n}\nProperty.textBaselineMapping = {\n  'baseline': 'alphabetic',\n  'before-edge': 'top',\n  'text-before-edge': 'top',\n  'middle': 'middle',\n  'central': 'middle',\n  'after-edge': 'bottom',\n  'text-after-edge': 'bottom',\n  'ideographic': 'ideographic',\n  'alphabetic': 'alphabetic',\n  'hanging': 'hanging',\n  'mathematical': 'alphabetic'\n};\n\nclass ViewPort {\n  constructor() {\n    this.viewPorts = [];\n  }\n\n  clear() {\n    this.viewPorts = [];\n  }\n\n  setCurrent(width, height) {\n    this.viewPorts.push({\n      width,\n      height\n    });\n  }\n\n  removeCurrent() {\n    this.viewPorts.pop();\n  }\n\n  getCurrent() {\n    var {\n      viewPorts\n    } = this;\n    return viewPorts[viewPorts.length - 1];\n  }\n\n  get width() {\n    return this.getCurrent().width;\n  }\n\n  get height() {\n    return this.getCurrent().height;\n  }\n\n  computeSize(d) {\n    if (typeof d === 'number') {\n      return d;\n    }\n\n    if (d === 'x') {\n      return this.width;\n    }\n\n    if (d === 'y') {\n      return this.height;\n    }\n\n    return Math.sqrt(Math.pow(this.width, 2) + Math.pow(this.height, 2)) / Math.sqrt(2);\n  }\n\n}\n\nclass Point {\n  constructor(x, y) {\n    this.x = x;\n    this.y = y;\n  }\n\n  static parse(point) {\n    var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    var [x = defaultValue, y = defaultValue] = toNumbers(point);\n    return new Point(x, y);\n  }\n\n  static parseScale(scale) {\n    var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n    var [x = defaultValue, y = x] = toNumbers(scale);\n    return new Point(x, y);\n  }\n\n  static parsePath(path) {\n    var points = toNumbers(path);\n    var len = points.length;\n    var pathPoints = [];\n\n    for (var i = 0; i < len; i += 2) {\n      pathPoints.push(new Point(points[i], points[i + 1]));\n    }\n\n    return pathPoints;\n  }\n\n  angleTo(point) {\n    return Math.atan2(point.y - this.y, point.x - this.x);\n  }\n\n  applyTransform(transform) {\n    var {\n      x,\n      y\n    } = this;\n    var xp = x * transform[0] + y * transform[2] + transform[4];\n    var yp = x * transform[1] + y * transform[3] + transform[5];\n    this.x = xp;\n    this.y = yp;\n  }\n\n}\n\nclass Mouse {\n  constructor(screen) {\n    this.screen = screen;\n    this.working = false;\n    this.events = [];\n    this.eventElements = []; // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n\n    this.onClick = this.onClick.bind(this); // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n\n    this.onMouseMove = this.onMouseMove.bind(this);\n  }\n\n  isWorking() {\n    return this.working;\n  }\n\n  start() {\n    if (this.working) {\n      return;\n    }\n\n    var {\n      screen,\n      onClick,\n      onMouseMove\n    } = this;\n    var canvas = screen.ctx.canvas;\n    canvas.onclick = onClick;\n    canvas.onmousemove = onMouseMove;\n    this.working = true;\n  }\n\n  stop() {\n    if (!this.working) {\n      return;\n    }\n\n    var canvas = this.screen.ctx.canvas;\n    this.working = false;\n    canvas.onclick = null;\n    canvas.onmousemove = null;\n  }\n\n  hasEvents() {\n    return this.working && this.events.length > 0;\n  }\n\n  runEvents() {\n    if (!this.working) {\n      return;\n    }\n\n    var {\n      screen: document,\n      events,\n      eventElements\n    } = this;\n    var {\n      style\n    } = document.ctx.canvas;\n\n    if (style) {\n      style.cursor = '';\n    }\n\n    events.forEach((_ref, i) => {\n      var {\n        run\n      } = _ref;\n      var element = eventElements[i];\n\n      while (element) {\n        run(element);\n        element = element.parent;\n      }\n    }); // done running, clear\n\n    this.events = [];\n    this.eventElements = [];\n  }\n\n  checkPath(element, ctx) {\n    if (!this.working || !ctx) {\n      return;\n    }\n\n    var {\n      events,\n      eventElements\n    } = this;\n    events.forEach((_ref2, i) => {\n      var {\n        x,\n        y\n      } = _ref2;\n\n      if (!eventElements[i] && ctx.isPointInPath && ctx.isPointInPath(x, y)) {\n        eventElements[i] = element;\n      }\n    });\n  }\n\n  checkBoundingBox(element, boundingBox) {\n    if (!this.working || !boundingBox) {\n      return;\n    }\n\n    var {\n      events,\n      eventElements\n    } = this;\n    events.forEach((_ref3, i) => {\n      var {\n        x,\n        y\n      } = _ref3;\n\n      if (!eventElements[i] && boundingBox.isPointInBox(x, y)) {\n        eventElements[i] = element;\n      }\n    });\n  }\n\n  mapXY(x, y) {\n    var {\n      window,\n      ctx\n    } = this.screen;\n    var point = new Point(x, y);\n    var element = ctx.canvas;\n\n    while (element) {\n      point.x -= element.offsetLeft;\n      point.y -= element.offsetTop;\n      element = element.offsetParent;\n    }\n\n    if (window.scrollX) {\n      point.x += window.scrollX;\n    }\n\n    if (window.scrollY) {\n      point.y += window.scrollY;\n    }\n\n    return point;\n  }\n\n  onClick(event) {\n    var {\n      x,\n      y\n    } = this.mapXY(event.clientX, event.clientY);\n    this.events.push({\n      type: 'onclick',\n      x,\n      y,\n\n      run(eventTarget) {\n        if (eventTarget.onClick) {\n          eventTarget.onClick();\n        }\n      }\n\n    });\n  }\n\n  onMouseMove(event) {\n    var {\n      x,\n      y\n    } = this.mapXY(event.clientX, event.clientY);\n    this.events.push({\n      type: 'onmousemove',\n      x,\n      y,\n\n      run(eventTarget) {\n        if (eventTarget.onMouseMove) {\n          eventTarget.onMouseMove();\n        }\n      }\n\n    });\n  }\n\n}\n\nvar defaultWindow = typeof window !== 'undefined' ? window : null;\nvar defaultFetch$1 = typeof fetch !== 'undefined' ? fetch.bind(undefined) // `fetch` depends on context: `someObject.fetch(...)` will throw error.\n: null;\nclass Screen {\n  constructor(ctx) {\n    var {\n      fetch = defaultFetch$1,\n      window = defaultWindow\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.ctx = ctx;\n    this.FRAMERATE = 30;\n    this.MAX_VIRTUAL_PIXELS = 30000;\n    this.CLIENT_WIDTH = 800;\n    this.CLIENT_HEIGHT = 600;\n    this.viewPort = new ViewPort();\n    this.mouse = new Mouse(this);\n    this.animations = [];\n    this.waits = [];\n    this.frameDuration = 0;\n    this.isReadyLock = false;\n    this.isFirstRender = true;\n    this.intervalId = null;\n    this.window = window;\n    this.fetch = fetch;\n  }\n\n  wait(checker) {\n    this.waits.push(checker);\n  }\n\n  ready() {\n    // eslint-disable-next-line @typescript-eslint/no-misused-promises\n    if (!this.readyPromise) {\n      return Promise.resolve();\n    }\n\n    return this.readyPromise;\n  }\n\n  isReady() {\n    if (this.isReadyLock) {\n      return true;\n    }\n\n    var isReadyLock = this.waits.every(_ => _());\n\n    if (isReadyLock) {\n      this.waits = [];\n\n      if (this.resolveReady) {\n        this.resolveReady();\n      }\n    }\n\n    this.isReadyLock = isReadyLock;\n    return isReadyLock;\n  }\n\n  setDefaults(ctx) {\n    // initial values and defaults\n    ctx.strokeStyle = 'rgba(0,0,0,0)';\n    ctx.lineCap = 'butt';\n    ctx.lineJoin = 'miter';\n    ctx.miterLimit = 4;\n  }\n\n  setViewBox(_ref) {\n    var {\n      document,\n      ctx,\n      aspectRatio,\n      width,\n      desiredWidth,\n      height,\n      desiredHeight,\n      minX = 0,\n      minY = 0,\n      refX,\n      refY,\n      clip = false,\n      clipX = 0,\n      clipY = 0\n    } = _ref;\n    // aspect ratio - http://www.w3.org/TR/SVG/coords.html#PreserveAspectRatioAttribute\n    var cleanAspectRatio = compressSpaces(aspectRatio).replace(/^defer\\s/, ''); // ignore defer\n\n    var [aspectRatioAlign, aspectRatioMeetOrSlice] = cleanAspectRatio.split(' ');\n    var align = aspectRatioAlign || 'xMidYMid';\n    var meetOrSlice = aspectRatioMeetOrSlice || 'meet'; // calculate scale\n\n    var scaleX = width / desiredWidth;\n    var scaleY = height / desiredHeight;\n    var scaleMin = Math.min(scaleX, scaleY);\n    var scaleMax = Math.max(scaleX, scaleY);\n    var finalDesiredWidth = desiredWidth;\n    var finalDesiredHeight = desiredHeight;\n\n    if (meetOrSlice === 'meet') {\n      finalDesiredWidth *= scaleMin;\n      finalDesiredHeight *= scaleMin;\n    }\n\n    if (meetOrSlice === 'slice') {\n      finalDesiredWidth *= scaleMax;\n      finalDesiredHeight *= scaleMax;\n    }\n\n    var refXProp = new Property(document, 'refX', refX);\n    var refYProp = new Property(document, 'refY', refY);\n    var hasRefs = refXProp.hasValue() && refYProp.hasValue();\n\n    if (hasRefs) {\n      ctx.translate(-scaleMin * refXProp.getPixels('x'), -scaleMin * refYProp.getPixels('y'));\n    }\n\n    if (clip) {\n      var scaledClipX = scaleMin * clipX;\n      var scaledClipY = scaleMin * clipY;\n      ctx.beginPath();\n      ctx.moveTo(scaledClipX, scaledClipY);\n      ctx.lineTo(width, scaledClipY);\n      ctx.lineTo(width, height);\n      ctx.lineTo(scaledClipX, height);\n      ctx.closePath();\n      ctx.clip();\n    }\n\n    if (!hasRefs) {\n      var isMeetMinY = meetOrSlice === 'meet' && scaleMin === scaleY;\n      var isSliceMaxY = meetOrSlice === 'slice' && scaleMax === scaleY;\n      var isMeetMinX = meetOrSlice === 'meet' && scaleMin === scaleX;\n      var isSliceMaxX = meetOrSlice === 'slice' && scaleMax === scaleX;\n\n      if (align.startsWith('xMid') && (isMeetMinY || isSliceMaxY)) {\n        ctx.translate(width / 2.0 - finalDesiredWidth / 2.0, 0);\n      }\n\n      if (align.endsWith('YMid') && (isMeetMinX || isSliceMaxX)) {\n        ctx.translate(0, height / 2.0 - finalDesiredHeight / 2.0);\n      }\n\n      if (align.startsWith('xMax') && (isMeetMinY || isSliceMaxY)) {\n        ctx.translate(width - finalDesiredWidth, 0);\n      }\n\n      if (align.endsWith('YMax') && (isMeetMinX || isSliceMaxX)) {\n        ctx.translate(0, height - finalDesiredHeight);\n      }\n    } // scale\n\n\n    switch (true) {\n      case align === 'none':\n        ctx.scale(scaleX, scaleY);\n        break;\n\n      case meetOrSlice === 'meet':\n        ctx.scale(scaleMin, scaleMin);\n        break;\n\n      case meetOrSlice === 'slice':\n        ctx.scale(scaleMax, scaleMax);\n        break;\n    } // translate\n\n\n    ctx.translate(-minX, -minY);\n  }\n\n  start(element) {\n    var {\n      enableRedraw = false,\n      ignoreMouse = false,\n      ignoreAnimation = false,\n      ignoreDimensions = false,\n      ignoreClear = false,\n      forceRedraw,\n      scaleWidth,\n      scaleHeight,\n      offsetX,\n      offsetY\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var {\n      FRAMERATE,\n      mouse\n    } = this;\n    var frameDuration = 1000 / FRAMERATE;\n    this.frameDuration = frameDuration;\n    this.readyPromise = new Promise(resolve => {\n      this.resolveReady = resolve;\n    });\n\n    if (this.isReady()) {\n      this.render(element, ignoreDimensions, ignoreClear, scaleWidth, scaleHeight, offsetX, offsetY);\n    }\n\n    if (!enableRedraw) {\n      return;\n    }\n\n    var now = Date.now();\n    var then = now;\n    var delta = 0;\n\n    var tick = () => {\n      now = Date.now();\n      delta = now - then;\n\n      if (delta >= frameDuration) {\n        then = now - delta % frameDuration;\n\n        if (this.shouldUpdate(ignoreAnimation, forceRedraw)) {\n          this.render(element, ignoreDimensions, ignoreClear, scaleWidth, scaleHeight, offsetX, offsetY);\n          mouse.runEvents();\n        }\n      }\n\n      this.intervalId = raf__WEBPACK_IMPORTED_MODULE_11__(tick);\n    };\n\n    if (!ignoreMouse) {\n      mouse.start();\n    }\n\n    this.intervalId = raf__WEBPACK_IMPORTED_MODULE_11__(tick);\n  }\n\n  stop() {\n    if (this.intervalId) {\n      raf__WEBPACK_IMPORTED_MODULE_11__.cancel(this.intervalId);\n      this.intervalId = null;\n    }\n\n    this.mouse.stop();\n  }\n\n  shouldUpdate(ignoreAnimation, forceRedraw) {\n    // need update from animations?\n    if (!ignoreAnimation) {\n      var {\n        frameDuration\n      } = this;\n      var shouldUpdate = this.animations.reduce((shouldUpdate, animation) => animation.update(frameDuration) || shouldUpdate, false);\n\n      if (shouldUpdate) {\n        return true;\n      }\n    } // need update from redraw?\n\n\n    if (typeof forceRedraw === 'function' && forceRedraw()) {\n      return true;\n    }\n\n    if (!this.isReadyLock && this.isReady()) {\n      return true;\n    } // need update from mouse events?\n\n\n    if (this.mouse.hasEvents()) {\n      return true;\n    }\n\n    return false;\n  }\n\n  render(element, ignoreDimensions, ignoreClear, scaleWidth, scaleHeight, offsetX, offsetY) {\n    var {\n      CLIENT_WIDTH,\n      CLIENT_HEIGHT,\n      viewPort,\n      ctx,\n      isFirstRender\n    } = this;\n    var canvas = ctx.canvas;\n    viewPort.clear();\n\n    if (canvas.width && canvas.height) {\n      viewPort.setCurrent(canvas.width, canvas.height);\n    } else {\n      viewPort.setCurrent(CLIENT_WIDTH, CLIENT_HEIGHT);\n    }\n\n    var widthStyle = element.getStyle('width');\n    var heightStyle = element.getStyle('height');\n\n    if (!ignoreDimensions && (isFirstRender || typeof scaleWidth !== 'number' && typeof scaleHeight !== 'number')) {\n      // set canvas size\n      if (widthStyle.hasValue()) {\n        canvas.width = widthStyle.getPixels('x');\n\n        if (canvas.style) {\n          canvas.style.width = \"\".concat(canvas.width, \"px\");\n        }\n      }\n\n      if (heightStyle.hasValue()) {\n        canvas.height = heightStyle.getPixels('y');\n\n        if (canvas.style) {\n          canvas.style.height = \"\".concat(canvas.height, \"px\");\n        }\n      }\n    }\n\n    var cWidth = canvas.clientWidth || canvas.width;\n    var cHeight = canvas.clientHeight || canvas.height;\n\n    if (ignoreDimensions && widthStyle.hasValue() && heightStyle.hasValue()) {\n      cWidth = widthStyle.getPixels('x');\n      cHeight = heightStyle.getPixels('y');\n    }\n\n    viewPort.setCurrent(cWidth, cHeight);\n\n    if (typeof offsetX === 'number') {\n      element.getAttribute('x', true).setValue(offsetX);\n    }\n\n    if (typeof offsetY === 'number') {\n      element.getAttribute('y', true).setValue(offsetY);\n    }\n\n    if (typeof scaleWidth === 'number' || typeof scaleHeight === 'number') {\n      var viewBox = toNumbers(element.getAttribute('viewBox').getString());\n      var xRatio = 0;\n      var yRatio = 0;\n\n      if (typeof scaleWidth === 'number') {\n        var _widthStyle = element.getStyle('width');\n\n        if (_widthStyle.hasValue()) {\n          xRatio = _widthStyle.getPixels('x') / scaleWidth;\n        } else if (!isNaN(viewBox[2])) {\n          xRatio = viewBox[2] / scaleWidth;\n        }\n      }\n\n      if (typeof scaleHeight === 'number') {\n        var _heightStyle = element.getStyle('height');\n\n        if (_heightStyle.hasValue()) {\n          yRatio = _heightStyle.getPixels('y') / scaleHeight;\n        } else if (!isNaN(viewBox[3])) {\n          yRatio = viewBox[3] / scaleHeight;\n        }\n      }\n\n      if (!xRatio) {\n        xRatio = yRatio;\n      }\n\n      if (!yRatio) {\n        yRatio = xRatio;\n      }\n\n      element.getAttribute('width', true).setValue(scaleWidth);\n      element.getAttribute('height', true).setValue(scaleHeight);\n      var transformStyle = element.getStyle('transform', true, true);\n      transformStyle.setValue(\"\".concat(transformStyle.getString(), \" scale(\").concat(1.0 / xRatio, \", \").concat(1.0 / yRatio, \")\"));\n    } // clear and render\n\n\n    if (!ignoreClear) {\n      ctx.clearRect(0, 0, cWidth, cHeight);\n    }\n\n    element.render(ctx);\n\n    if (isFirstRender) {\n      this.isFirstRender = false;\n    }\n  }\n\n}\nScreen.defaultWindow = defaultWindow;\nScreen.defaultFetch = defaultFetch$1;\n\nvar {\n  defaultFetch\n} = Screen;\nvar DefaultDOMParser = typeof DOMParser !== 'undefined' ? DOMParser : null;\nclass Parser {\n  constructor() {\n    var {\n      fetch = defaultFetch,\n      DOMParser = DefaultDOMParser\n    } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    this.fetch = fetch;\n    this.DOMParser = DOMParser;\n  }\n\n  parse(resource) {\n    var _this = this;\n\n    return _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* () {\n      if (resource.startsWith('<')) {\n        return _this.parseFromString(resource);\n      }\n\n      return _this.load(resource);\n    })();\n  }\n\n  parseFromString(xml) {\n    var parser = new this.DOMParser();\n\n    try {\n      return this.checkDocument(parser.parseFromString(xml, 'image/svg+xml'));\n    } catch (err) {\n      return this.checkDocument(parser.parseFromString(xml, 'text/xml'));\n    }\n  }\n\n  checkDocument(document) {\n    var parserError = document.getElementsByTagName('parsererror')[0];\n\n    if (parserError) {\n      throw new Error(parserError.textContent);\n    }\n\n    return document;\n  }\n\n  load(url) {\n    var _this2 = this;\n\n    return _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* () {\n      var response = yield _this2.fetch(url);\n      var xml = yield response.text();\n      return _this2.parseFromString(xml);\n    })();\n  }\n\n}\n\nclass Translate {\n  constructor(_, point) {\n    this.type = 'translate';\n    this.point = null;\n    this.point = Point.parse(point);\n  }\n\n  apply(ctx) {\n    var {\n      x,\n      y\n    } = this.point;\n    ctx.translate(x || 0.0, y || 0.0);\n  }\n\n  unapply(ctx) {\n    var {\n      x,\n      y\n    } = this.point;\n    ctx.translate(-1.0 * x || 0.0, -1.0 * y || 0.0);\n  }\n\n  applyToPoint(point) {\n    var {\n      x,\n      y\n    } = this.point;\n    point.applyTransform([1, 0, 0, 1, x || 0.0, y || 0.0]);\n  }\n\n}\n\nclass Rotate {\n  constructor(document, rotate, transformOrigin) {\n    this.type = 'rotate';\n    this.angle = null;\n    this.originX = null;\n    this.originY = null;\n    this.cx = 0;\n    this.cy = 0;\n    var numbers = toNumbers(rotate);\n    this.angle = new Property(document, 'angle', numbers[0]);\n    this.originX = transformOrigin[0];\n    this.originY = transformOrigin[1];\n    this.cx = numbers[1] || 0;\n    this.cy = numbers[2] || 0;\n  }\n\n  apply(ctx) {\n    var {\n      cx,\n      cy,\n      originX,\n      originY,\n      angle\n    } = this;\n    var tx = cx + originX.getPixels('x');\n    var ty = cy + originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.rotate(angle.getRadians());\n    ctx.translate(-tx, -ty);\n  }\n\n  unapply(ctx) {\n    var {\n      cx,\n      cy,\n      originX,\n      originY,\n      angle\n    } = this;\n    var tx = cx + originX.getPixels('x');\n    var ty = cy + originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.rotate(-1.0 * angle.getRadians());\n    ctx.translate(-tx, -ty);\n  }\n\n  applyToPoint(point) {\n    var {\n      cx,\n      cy,\n      angle\n    } = this;\n    var rad = angle.getRadians();\n    point.applyTransform([1, 0, 0, 1, cx || 0.0, cy || 0.0 // this.p.y\n    ]);\n    point.applyTransform([Math.cos(rad), Math.sin(rad), -Math.sin(rad), Math.cos(rad), 0, 0]);\n    point.applyTransform([1, 0, 0, 1, -cx || 0.0, -cy || 0.0 // -this.p.y\n    ]);\n  }\n\n}\n\nclass Scale {\n  constructor(_, scale, transformOrigin) {\n    this.type = 'scale';\n    this.scale = null;\n    this.originX = null;\n    this.originY = null;\n    var scaleSize = Point.parseScale(scale); // Workaround for node-canvas\n\n    if (scaleSize.x === 0 || scaleSize.y === 0) {\n      scaleSize.x = PSEUDO_ZERO;\n      scaleSize.y = PSEUDO_ZERO;\n    }\n\n    this.scale = scaleSize;\n    this.originX = transformOrigin[0];\n    this.originY = transformOrigin[1];\n  }\n\n  apply(ctx) {\n    var {\n      scale: {\n        x,\n        y\n      },\n      originX,\n      originY\n    } = this;\n    var tx = originX.getPixels('x');\n    var ty = originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.scale(x, y || x);\n    ctx.translate(-tx, -ty);\n  }\n\n  unapply(ctx) {\n    var {\n      scale: {\n        x,\n        y\n      },\n      originX,\n      originY\n    } = this;\n    var tx = originX.getPixels('x');\n    var ty = originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.scale(1.0 / x, 1.0 / y || x);\n    ctx.translate(-tx, -ty);\n  }\n\n  applyToPoint(point) {\n    var {\n      x,\n      y\n    } = this.scale;\n    point.applyTransform([x || 0.0, 0, 0, y || 0.0, 0, 0]);\n  }\n\n}\n\nclass Matrix {\n  constructor(_, matrix, transformOrigin) {\n    this.type = 'matrix';\n    this.matrix = [];\n    this.originX = null;\n    this.originY = null;\n    this.matrix = toNumbers(matrix);\n    this.originX = transformOrigin[0];\n    this.originY = transformOrigin[1];\n  }\n\n  apply(ctx) {\n    var {\n      originX,\n      originY,\n      matrix\n    } = this;\n    var tx = originX.getPixels('x');\n    var ty = originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.transform(matrix[0], matrix[1], matrix[2], matrix[3], matrix[4], matrix[5]);\n    ctx.translate(-tx, -ty);\n  }\n\n  unapply(ctx) {\n    var {\n      originX,\n      originY,\n      matrix\n    } = this;\n    var a = matrix[0];\n    var b = matrix[2];\n    var c = matrix[4];\n    var d = matrix[1];\n    var e = matrix[3];\n    var f = matrix[5];\n    var g = 0.0;\n    var h = 0.0;\n    var i = 1.0;\n    var det = 1 / (a * (e * i - f * h) - b * (d * i - f * g) + c * (d * h - e * g));\n    var tx = originX.getPixels('x');\n    var ty = originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.transform(det * (e * i - f * h), det * (f * g - d * i), det * (c * h - b * i), det * (a * i - c * g), det * (b * f - c * e), det * (c * d - a * f));\n    ctx.translate(-tx, -ty);\n  }\n\n  applyToPoint(point) {\n    point.applyTransform(this.matrix);\n  }\n\n}\n\nclass Skew extends Matrix {\n  constructor(document, skew, transformOrigin) {\n    super(document, skew, transformOrigin);\n    this.type = 'skew';\n    this.angle = null;\n    this.angle = new Property(document, 'angle', skew);\n  }\n\n}\n\nclass SkewX extends Skew {\n  constructor(document, skew, transformOrigin) {\n    super(document, skew, transformOrigin);\n    this.type = 'skewX';\n    this.matrix = [1, 0, Math.tan(this.angle.getRadians()), 1, 0, 0];\n  }\n\n}\n\nclass SkewY extends Skew {\n  constructor(document, skew, transformOrigin) {\n    super(document, skew, transformOrigin);\n    this.type = 'skewY';\n    this.matrix = [1, Math.tan(this.angle.getRadians()), 0, 1, 0, 0];\n  }\n\n}\n\nfunction parseTransforms(transform) {\n  return compressSpaces(transform).trim().replace(/\\)([a-zA-Z])/g, ') $1').replace(/\\)(\\s?,\\s?)/g, ') ').split(/\\s(?=[a-z])/);\n}\n\nfunction parseTransform(transform) {\n  var [type, value] = transform.split('(');\n  return [type.trim(), value.trim().replace(')', '')];\n}\n\nclass Transform {\n  constructor(document, transform, transformOrigin) {\n    this.document = document;\n    this.transforms = [];\n    var data = parseTransforms(transform);\n    data.forEach(transform => {\n      if (transform === 'none') {\n        return;\n      }\n\n      var [type, value] = parseTransform(transform);\n      var TransformType = Transform.transformTypes[type];\n\n      if (typeof TransformType !== 'undefined') {\n        this.transforms.push(new TransformType(this.document, value, transformOrigin));\n      }\n    });\n  }\n\n  static fromElement(document, element) {\n    var transformStyle = element.getStyle('transform', false, true);\n    var [transformOriginXProperty, transformOriginYProperty = transformOriginXProperty] = element.getStyle('transform-origin', false, true).split();\n    var transformOrigin = [transformOriginXProperty, transformOriginYProperty];\n\n    if (transformStyle.hasValue()) {\n      return new Transform(document, transformStyle.getString(), transformOrigin);\n    }\n\n    return null;\n  }\n\n  apply(ctx) {\n    var {\n      transforms\n    } = this;\n    var len = transforms.length;\n\n    for (var i = 0; i < len; i++) {\n      transforms[i].apply(ctx);\n    }\n  }\n\n  unapply(ctx) {\n    var {\n      transforms\n    } = this;\n    var len = transforms.length;\n\n    for (var i = len - 1; i >= 0; i--) {\n      transforms[i].unapply(ctx);\n    }\n  } // TODO: applyToPoint unused ... remove?\n\n\n  applyToPoint(point) {\n    var {\n      transforms\n    } = this;\n    var len = transforms.length;\n\n    for (var i = 0; i < len; i++) {\n      transforms[i].applyToPoint(point);\n    }\n  }\n\n}\nTransform.transformTypes = {\n  translate: Translate,\n  rotate: Rotate,\n  scale: Scale,\n  matrix: Matrix,\n  skewX: SkewX,\n  skewY: SkewY\n};\n\nclass Element {\n  constructor(document, node) {\n    var captureTextNodes = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    this.document = document;\n    this.node = node;\n    this.captureTextNodes = captureTextNodes;\n    this.attributes = Object.create(null);\n    this.styles = Object.create(null);\n    this.stylesSpecificity = Object.create(null);\n    this.animationFrozen = false;\n    this.animationFrozenValue = '';\n    this.parent = null;\n    this.children = [];\n\n    if (!node || node.nodeType !== 1) {\n      // ELEMENT_NODE\n      return;\n    } // add attributes\n\n\n    Array.from(node.attributes).forEach(attribute => {\n      var nodeName = normalizeAttributeName(attribute.nodeName);\n      this.attributes[nodeName] = new Property(document, nodeName, attribute.value);\n    });\n    this.addStylesFromStyleDefinition(); // add inline styles\n\n    if (this.getAttribute('style').hasValue()) {\n      var styles = this.getAttribute('style').getString().split(';').map(_ => _.trim());\n      styles.forEach(style => {\n        if (!style) {\n          return;\n        }\n\n        var [name, value] = style.split(':').map(_ => _.trim());\n        this.styles[name] = new Property(document, name, value);\n      });\n    }\n\n    var {\n      definitions\n    } = document;\n    var id = this.getAttribute('id'); // add id\n\n    if (id.hasValue()) {\n      if (!definitions[id.getString()]) {\n        definitions[id.getString()] = this;\n      }\n    }\n\n    Array.from(node.childNodes).forEach(childNode => {\n      if (childNode.nodeType === 1) {\n        this.addChild(childNode); // ELEMENT_NODE\n      } else if (captureTextNodes && (childNode.nodeType === 3 || childNode.nodeType === 4)) {\n        var textNode = document.createTextNode(childNode);\n\n        if (textNode.getText().length > 0) {\n          this.addChild(textNode); // TEXT_NODE\n        }\n      }\n    });\n  }\n\n  getAttribute(name) {\n    var createIfNotExists = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var attr = this.attributes[name];\n\n    if (!attr && createIfNotExists) {\n      var _attr = new Property(this.document, name, '');\n\n      this.attributes[name] = _attr;\n      return _attr;\n    }\n\n    return attr || Property.empty(this.document);\n  }\n\n  getHrefAttribute() {\n    for (var key in this.attributes) {\n      if (key === 'href' || key.endsWith(':href')) {\n        return this.attributes[key];\n      }\n    }\n\n    return Property.empty(this.document);\n  }\n\n  getStyle(name) {\n    var createIfNotExists = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var skipAncestors = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    var style = this.styles[name];\n\n    if (style) {\n      return style;\n    }\n\n    var attr = this.getAttribute(name);\n\n    if (attr !== null && attr !== void 0 && attr.hasValue()) {\n      this.styles[name] = attr; // move up to me to cache\n\n      return attr;\n    }\n\n    if (!skipAncestors) {\n      var {\n        parent\n      } = this;\n\n      if (parent) {\n        var parentStyle = parent.getStyle(name);\n\n        if (parentStyle !== null && parentStyle !== void 0 && parentStyle.hasValue()) {\n          return parentStyle;\n        }\n      }\n    }\n\n    if (createIfNotExists) {\n      var _style = new Property(this.document, name, '');\n\n      this.styles[name] = _style;\n      return _style;\n    }\n\n    return style || Property.empty(this.document);\n  }\n\n  render(ctx) {\n    // don't render display=none\n    // don't render visibility=hidden\n    if (this.getStyle('display').getString() === 'none' || this.getStyle('visibility').getString() === 'hidden') {\n      return;\n    }\n\n    ctx.save();\n\n    if (this.getStyle('mask').hasValue()) {\n      // mask\n      var mask = this.getStyle('mask').getDefinition();\n\n      if (mask) {\n        this.applyEffects(ctx);\n        mask.apply(ctx, this);\n      }\n    } else if (this.getStyle('filter').getValue('none') !== 'none') {\n      // filter\n      var filter = this.getStyle('filter').getDefinition();\n\n      if (filter) {\n        this.applyEffects(ctx);\n        filter.apply(ctx, this);\n      }\n    } else {\n      this.setContext(ctx);\n      this.renderChildren(ctx);\n      this.clearContext(ctx);\n    }\n\n    ctx.restore();\n  }\n\n  setContext(_) {// NO RENDER\n  }\n\n  applyEffects(ctx) {\n    // transform\n    var transform = Transform.fromElement(this.document, this);\n\n    if (transform) {\n      transform.apply(ctx);\n    } // clip\n\n\n    var clipPathStyleProp = this.getStyle('clip-path', false, true);\n\n    if (clipPathStyleProp.hasValue()) {\n      var clip = clipPathStyleProp.getDefinition();\n\n      if (clip) {\n        clip.apply(ctx);\n      }\n    }\n  }\n\n  clearContext(_) {// NO RENDER\n  }\n\n  renderChildren(ctx) {\n    this.children.forEach(child => {\n      child.render(ctx);\n    });\n  }\n\n  addChild(childNode) {\n    var child = childNode instanceof Element ? childNode : this.document.createElement(childNode);\n    child.parent = this;\n\n    if (!Element.ignoreChildTypes.includes(child.type)) {\n      this.children.push(child);\n    }\n  }\n\n  matchesSelector(selector) {\n    var _node$getAttribute;\n\n    var {\n      node\n    } = this;\n\n    if (typeof node.matches === 'function') {\n      return node.matches(selector);\n    }\n\n    var styleClasses = (_node$getAttribute = node.getAttribute) === null || _node$getAttribute === void 0 ? void 0 : _node$getAttribute.call(node, 'class');\n\n    if (!styleClasses || styleClasses === '') {\n      return false;\n    }\n\n    return styleClasses.split(' ').some(styleClass => \".\".concat(styleClass) === selector);\n  }\n\n  addStylesFromStyleDefinition() {\n    var {\n      styles,\n      stylesSpecificity\n    } = this.document;\n\n    for (var selector in styles) {\n      if (!selector.startsWith('@') && this.matchesSelector(selector)) {\n        var style = styles[selector];\n        var specificity = stylesSpecificity[selector];\n\n        if (style) {\n          for (var name in style) {\n            var existingSpecificity = this.stylesSpecificity[name];\n\n            if (typeof existingSpecificity === 'undefined') {\n              existingSpecificity = '000';\n            }\n\n            if (specificity >= existingSpecificity) {\n              this.styles[name] = style[name];\n              this.stylesSpecificity[name] = specificity;\n            }\n          }\n        }\n      }\n    }\n  }\n\n  removeStyles(element, ignoreStyles) {\n    var toRestore = ignoreStyles.reduce((toRestore, name) => {\n      var styleProp = element.getStyle(name);\n\n      if (!styleProp.hasValue()) {\n        return toRestore;\n      }\n\n      var value = styleProp.getString();\n      styleProp.setValue('');\n      return [...toRestore, [name, value]];\n    }, []);\n    return toRestore;\n  }\n\n  restoreStyles(element, styles) {\n    styles.forEach(_ref => {\n      var [name, value] = _ref;\n      element.getStyle(name, true).setValue(value);\n    });\n  }\n\n  isFirstChild() {\n    var _this$parent;\n\n    return ((_this$parent = this.parent) === null || _this$parent === void 0 ? void 0 : _this$parent.children.indexOf(this)) === 0;\n  }\n\n}\nElement.ignoreChildTypes = ['title'];\n\nclass UnknownElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n  }\n\n}\n\nfunction wrapFontFamily(fontFamily) {\n  var trimmed = fontFamily.trim();\n  return /^('|\")/.test(trimmed) ? trimmed : \"\\\"\".concat(trimmed, \"\\\"\");\n}\n\nfunction prepareFontFamily(fontFamily) {\n  return typeof process === 'undefined' ? fontFamily : fontFamily.trim().split(',').map(wrapFontFamily).join(',');\n}\n/**\r\n * https://developer.mozilla.org/en-US/docs/Web/CSS/font-style\r\n * @param fontStyle\r\n * @returns CSS font style.\r\n */\n\n\nfunction prepareFontStyle(fontStyle) {\n  if (!fontStyle) {\n    return '';\n  }\n\n  var targetFontStyle = fontStyle.trim().toLowerCase();\n\n  switch (targetFontStyle) {\n    case 'normal':\n    case 'italic':\n    case 'oblique':\n    case 'inherit':\n    case 'initial':\n    case 'unset':\n      return targetFontStyle;\n\n    default:\n      if (/^oblique\\s+(-|)\\d+deg$/.test(targetFontStyle)) {\n        return targetFontStyle;\n      }\n\n      return '';\n  }\n}\n/**\r\n * https://developer.mozilla.org/en-US/docs/Web/CSS/font-weight\r\n * @param fontWeight\r\n * @returns CSS font weight.\r\n */\n\n\nfunction prepareFontWeight(fontWeight) {\n  if (!fontWeight) {\n    return '';\n  }\n\n  var targetFontWeight = fontWeight.trim().toLowerCase();\n\n  switch (targetFontWeight) {\n    case 'normal':\n    case 'bold':\n    case 'lighter':\n    case 'bolder':\n    case 'inherit':\n    case 'initial':\n    case 'unset':\n      return targetFontWeight;\n\n    default:\n      if (/^[\\d.]+$/.test(targetFontWeight)) {\n        return targetFontWeight;\n      }\n\n      return '';\n  }\n}\n\nclass Font {\n  constructor(fontStyle, fontVariant, fontWeight, fontSize, fontFamily, inherit) {\n    var inheritFont = inherit ? typeof inherit === 'string' ? Font.parse(inherit) : inherit : {};\n    this.fontFamily = fontFamily || inheritFont.fontFamily;\n    this.fontSize = fontSize || inheritFont.fontSize;\n    this.fontStyle = fontStyle || inheritFont.fontStyle;\n    this.fontWeight = fontWeight || inheritFont.fontWeight;\n    this.fontVariant = fontVariant || inheritFont.fontVariant;\n  }\n\n  static parse() {\n    var font = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n    var inherit = arguments.length > 1 ? arguments[1] : undefined;\n    var fontStyle = '';\n    var fontVariant = '';\n    var fontWeight = '';\n    var fontSize = '';\n    var fontFamily = '';\n    var parts = compressSpaces(font).trim().split(' ');\n    var set = {\n      fontSize: false,\n      fontStyle: false,\n      fontWeight: false,\n      fontVariant: false\n    };\n    parts.forEach(part => {\n      switch (true) {\n        case !set.fontStyle && Font.styles.includes(part):\n          if (part !== 'inherit') {\n            fontStyle = part;\n          }\n\n          set.fontStyle = true;\n          break;\n\n        case !set.fontVariant && Font.variants.includes(part):\n          if (part !== 'inherit') {\n            fontVariant = part;\n          }\n\n          set.fontStyle = true;\n          set.fontVariant = true;\n          break;\n\n        case !set.fontWeight && Font.weights.includes(part):\n          if (part !== 'inherit') {\n            fontWeight = part;\n          }\n\n          set.fontStyle = true;\n          set.fontVariant = true;\n          set.fontWeight = true;\n          break;\n\n        case !set.fontSize:\n          if (part !== 'inherit') {\n            [fontSize] = part.split('/');\n          }\n\n          set.fontStyle = true;\n          set.fontVariant = true;\n          set.fontWeight = true;\n          set.fontSize = true;\n          break;\n\n        default:\n          if (part !== 'inherit') {\n            fontFamily += part;\n          }\n\n      }\n    });\n    return new Font(fontStyle, fontVariant, fontWeight, fontSize, fontFamily, inherit);\n  }\n\n  toString() {\n    return [prepareFontStyle(this.fontStyle), this.fontVariant, prepareFontWeight(this.fontWeight), this.fontSize, // Wrap fontFamily only on nodejs and only for canvas.ctx\n    prepareFontFamily(this.fontFamily)].join(' ').trim();\n  }\n\n}\nFont.styles = 'normal|italic|oblique|inherit';\nFont.variants = 'normal|small-caps|inherit';\nFont.weights = 'normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit';\n\nclass BoundingBox {\n  constructor() {\n    var x1 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : Number.NaN;\n    var y1 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Number.NaN;\n    var x2 = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Number.NaN;\n    var y2 = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : Number.NaN;\n    this.x1 = x1;\n    this.y1 = y1;\n    this.x2 = x2;\n    this.y2 = y2;\n    this.addPoint(x1, y1);\n    this.addPoint(x2, y2);\n  }\n\n  get x() {\n    return this.x1;\n  }\n\n  get y() {\n    return this.y1;\n  }\n\n  get width() {\n    return this.x2 - this.x1;\n  }\n\n  get height() {\n    return this.y2 - this.y1;\n  }\n\n  addPoint(x, y) {\n    if (typeof x !== 'undefined') {\n      if (isNaN(this.x1) || isNaN(this.x2)) {\n        this.x1 = x;\n        this.x2 = x;\n      }\n\n      if (x < this.x1) {\n        this.x1 = x;\n      }\n\n      if (x > this.x2) {\n        this.x2 = x;\n      }\n    }\n\n    if (typeof y !== 'undefined') {\n      if (isNaN(this.y1) || isNaN(this.y2)) {\n        this.y1 = y;\n        this.y2 = y;\n      }\n\n      if (y < this.y1) {\n        this.y1 = y;\n      }\n\n      if (y > this.y2) {\n        this.y2 = y;\n      }\n    }\n  }\n\n  addX(x) {\n    this.addPoint(x, null);\n  }\n\n  addY(y) {\n    this.addPoint(null, y);\n  }\n\n  addBoundingBox(boundingBox) {\n    if (!boundingBox) {\n      return;\n    }\n\n    var {\n      x1,\n      y1,\n      x2,\n      y2\n    } = boundingBox;\n    this.addPoint(x1, y1);\n    this.addPoint(x2, y2);\n  }\n\n  sumCubic(t, p0, p1, p2, p3) {\n    return Math.pow(1 - t, 3) * p0 + 3 * Math.pow(1 - t, 2) * t * p1 + 3 * (1 - t) * Math.pow(t, 2) * p2 + Math.pow(t, 3) * p3;\n  }\n\n  bezierCurveAdd(forX, p0, p1, p2, p3) {\n    var b = 6 * p0 - 12 * p1 + 6 * p2;\n    var a = -3 * p0 + 9 * p1 - 9 * p2 + 3 * p3;\n    var c = 3 * p1 - 3 * p0;\n\n    if (a === 0) {\n      if (b === 0) {\n        return;\n      }\n\n      var t = -c / b;\n\n      if (0 < t && t < 1) {\n        if (forX) {\n          this.addX(this.sumCubic(t, p0, p1, p2, p3));\n        } else {\n          this.addY(this.sumCubic(t, p0, p1, p2, p3));\n        }\n      }\n\n      return;\n    }\n\n    var b2ac = Math.pow(b, 2) - 4 * c * a;\n\n    if (b2ac < 0) {\n      return;\n    }\n\n    var t1 = (-b + Math.sqrt(b2ac)) / (2 * a);\n\n    if (0 < t1 && t1 < 1) {\n      if (forX) {\n        this.addX(this.sumCubic(t1, p0, p1, p2, p3));\n      } else {\n        this.addY(this.sumCubic(t1, p0, p1, p2, p3));\n      }\n    }\n\n    var t2 = (-b - Math.sqrt(b2ac)) / (2 * a);\n\n    if (0 < t2 && t2 < 1) {\n      if (forX) {\n        this.addX(this.sumCubic(t2, p0, p1, p2, p3));\n      } else {\n        this.addY(this.sumCubic(t2, p0, p1, p2, p3));\n      }\n    }\n  } // from http://blog.hackers-cafe.net/2009/06/how-to-calculate-bezier-curves-bounding.html\n\n\n  addBezierCurve(p0x, p0y, p1x, p1y, p2x, p2y, p3x, p3y) {\n    this.addPoint(p0x, p0y);\n    this.addPoint(p3x, p3y);\n    this.bezierCurveAdd(true, p0x, p1x, p2x, p3x);\n    this.bezierCurveAdd(false, p0y, p1y, p2y, p3y);\n  }\n\n  addQuadraticCurve(p0x, p0y, p1x, p1y, p2x, p2y) {\n    var cp1x = p0x + 2 / 3 * (p1x - p0x); // CP1 = QP0 + 2/3 *(QP1-QP0)\n\n    var cp1y = p0y + 2 / 3 * (p1y - p0y); // CP1 = QP0 + 2/3 *(QP1-QP0)\n\n    var cp2x = cp1x + 1 / 3 * (p2x - p0x); // CP2 = CP1 + 1/3 *(QP2-QP0)\n\n    var cp2y = cp1y + 1 / 3 * (p2y - p0y); // CP2 = CP1 + 1/3 *(QP2-QP0)\n\n    this.addBezierCurve(p0x, p0y, cp1x, cp2x, cp1y, cp2y, p2x, p2y);\n  }\n\n  isPointInBox(x, y) {\n    var {\n      x1,\n      y1,\n      x2,\n      y2\n    } = this;\n    return x1 <= x && x <= x2 && y1 <= y && y <= y2;\n  }\n\n}\n\nclass PathParser extends svg_pathdata__WEBPACK_IMPORTED_MODULE_17__.SVGPathData {\n  constructor(path) {\n    super(path // Fix spaces after signs.\n    .replace(/([+\\-.])\\s+/gm, '$1') // Remove invalid part.\n    .replace(/[^MmZzLlHhVvCcSsQqTtAae\\d\\s.,+-].*/g, ''));\n    this.control = null;\n    this.start = null;\n    this.current = null;\n    this.command = null;\n    this.commands = this.commands;\n    this.i = -1;\n    this.previousCommand = null;\n    this.points = [];\n    this.angles = [];\n  }\n\n  reset() {\n    this.i = -1;\n    this.command = null;\n    this.previousCommand = null;\n    this.start = new Point(0, 0);\n    this.control = new Point(0, 0);\n    this.current = new Point(0, 0);\n    this.points = [];\n    this.angles = [];\n  }\n\n  isEnd() {\n    var {\n      i,\n      commands\n    } = this;\n    return i >= commands.length - 1;\n  }\n\n  next() {\n    var command = this.commands[++this.i];\n    this.previousCommand = this.command;\n    this.command = command;\n    return command;\n  }\n\n  getPoint() {\n    var xProp = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'x';\n    var yProp = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'y';\n    var point = new Point(this.command[xProp], this.command[yProp]);\n    return this.makeAbsolute(point);\n  }\n\n  getAsControlPoint(xProp, yProp) {\n    var point = this.getPoint(xProp, yProp);\n    this.control = point;\n    return point;\n  }\n\n  getAsCurrentPoint(xProp, yProp) {\n    var point = this.getPoint(xProp, yProp);\n    this.current = point;\n    return point;\n  }\n\n  getReflectedControlPoint() {\n    var previousCommand = this.previousCommand.type;\n\n    if (previousCommand !== svg_pathdata__WEBPACK_IMPORTED_MODULE_17__.SVGPathData.CURVE_TO && previousCommand !== svg_pathdata__WEBPACK_IMPORTED_MODULE_17__.SVGPathData.SMOOTH_CURVE_TO && previousCommand !== svg_pathdata__WEBPACK_IMPORTED_MODULE_17__.SVGPathData.QUAD_TO && previousCommand !== svg_pathdata__WEBPACK_IMPORTED_MODULE_17__.SVGPathData.SMOOTH_QUAD_TO) {\n      return this.current;\n    } // reflect point\n\n\n    var {\n      current: {\n        x: cx,\n        y: cy\n      },\n      control: {\n        x: ox,\n        y: oy\n      }\n    } = this;\n    var point = new Point(2 * cx - ox, 2 * cy - oy);\n    return point;\n  }\n\n  makeAbsolute(point) {\n    if (this.command.relative) {\n      var {\n        x,\n        y\n      } = this.current;\n      point.x += x;\n      point.y += y;\n    }\n\n    return point;\n  }\n\n  addMarker(point, from, priorTo) {\n    var {\n      points,\n      angles\n    } = this; // if the last angle isn't filled in because we didn't have this point yet ...\n\n    if (priorTo && angles.length > 0 && !angles[angles.length - 1]) {\n      angles[angles.length - 1] = points[points.length - 1].angleTo(priorTo);\n    }\n\n    this.addMarkerAngle(point, from ? from.angleTo(point) : null);\n  }\n\n  addMarkerAngle(point, angle) {\n    this.points.push(point);\n    this.angles.push(angle);\n  }\n\n  getMarkerPoints() {\n    return this.points;\n  }\n\n  getMarkerAngles() {\n    var {\n      angles\n    } = this;\n    var len = angles.length;\n\n    for (var i = 0; i < len; i++) {\n      if (!angles[i]) {\n        for (var j = i + 1; j < len; j++) {\n          if (angles[j]) {\n            angles[i] = angles[j];\n            break;\n          }\n        }\n      }\n    }\n\n    return angles;\n  }\n\n}\n\nclass RenderedElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.modifiedEmSizeStack = false;\n  }\n\n  calculateOpacity() {\n    var opacity = 1.0; // eslint-disable-next-line @typescript-eslint/no-this-alias, consistent-this\n\n    var element = this;\n\n    while (element) {\n      var opacityStyle = element.getStyle('opacity', false, true); // no ancestors on style call\n\n      if (opacityStyle.hasValue(true)) {\n        opacity *= opacityStyle.getNumber();\n      }\n\n      element = element.parent;\n    }\n\n    return opacity;\n  }\n\n  setContext(ctx) {\n    var fromMeasure = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n    if (!fromMeasure) {\n      // causes stack overflow when measuring text with gradients\n      // fill\n      var fillStyleProp = this.getStyle('fill');\n      var fillOpacityStyleProp = this.getStyle('fill-opacity');\n      var strokeStyleProp = this.getStyle('stroke');\n      var strokeOpacityProp = this.getStyle('stroke-opacity');\n\n      if (fillStyleProp.isUrlDefinition()) {\n        var fillStyle = fillStyleProp.getFillStyleDefinition(this, fillOpacityStyleProp);\n\n        if (fillStyle) {\n          ctx.fillStyle = fillStyle;\n        }\n      } else if (fillStyleProp.hasValue()) {\n        if (fillStyleProp.getString() === 'currentColor') {\n          fillStyleProp.setValue(this.getStyle('color').getColor());\n        }\n\n        var _fillStyle = fillStyleProp.getColor();\n\n        if (_fillStyle !== 'inherit') {\n          ctx.fillStyle = _fillStyle === 'none' ? 'rgba(0,0,0,0)' : _fillStyle;\n        }\n      }\n\n      if (fillOpacityStyleProp.hasValue()) {\n        var _fillStyle2 = new Property(this.document, 'fill', ctx.fillStyle).addOpacity(fillOpacityStyleProp).getColor();\n\n        ctx.fillStyle = _fillStyle2;\n      } // stroke\n\n\n      if (strokeStyleProp.isUrlDefinition()) {\n        var strokeStyle = strokeStyleProp.getFillStyleDefinition(this, strokeOpacityProp);\n\n        if (strokeStyle) {\n          ctx.strokeStyle = strokeStyle;\n        }\n      } else if (strokeStyleProp.hasValue()) {\n        if (strokeStyleProp.getString() === 'currentColor') {\n          strokeStyleProp.setValue(this.getStyle('color').getColor());\n        }\n\n        var _strokeStyle = strokeStyleProp.getString();\n\n        if (_strokeStyle !== 'inherit') {\n          ctx.strokeStyle = _strokeStyle === 'none' ? 'rgba(0,0,0,0)' : _strokeStyle;\n        }\n      }\n\n      if (strokeOpacityProp.hasValue()) {\n        var _strokeStyle2 = new Property(this.document, 'stroke', ctx.strokeStyle).addOpacity(strokeOpacityProp).getString();\n\n        ctx.strokeStyle = _strokeStyle2;\n      }\n\n      var strokeWidthStyleProp = this.getStyle('stroke-width');\n\n      if (strokeWidthStyleProp.hasValue()) {\n        var newLineWidth = strokeWidthStyleProp.getPixels();\n        ctx.lineWidth = !newLineWidth ? PSEUDO_ZERO // browsers don't respect 0 (or node-canvas? :-)\n        : newLineWidth;\n      }\n\n      var strokeLinecapStyleProp = this.getStyle('stroke-linecap');\n      var strokeLinejoinStyleProp = this.getStyle('stroke-linejoin');\n      var strokeMiterlimitProp = this.getStyle('stroke-miterlimit'); // NEED TEST\n      // const pointOrderStyleProp = this.getStyle('paint-order');\n\n      var strokeDasharrayStyleProp = this.getStyle('stroke-dasharray');\n      var strokeDashoffsetProp = this.getStyle('stroke-dashoffset');\n\n      if (strokeLinecapStyleProp.hasValue()) {\n        ctx.lineCap = strokeLinecapStyleProp.getString();\n      }\n\n      if (strokeLinejoinStyleProp.hasValue()) {\n        ctx.lineJoin = strokeLinejoinStyleProp.getString();\n      }\n\n      if (strokeMiterlimitProp.hasValue()) {\n        ctx.miterLimit = strokeMiterlimitProp.getNumber();\n      } // NEED TEST\n      // if (pointOrderStyleProp.hasValue()) {\n      // \t// ?\n      // \tctx.paintOrder = pointOrderStyleProp.getValue();\n      // }\n\n\n      if (strokeDasharrayStyleProp.hasValue() && strokeDasharrayStyleProp.getString() !== 'none') {\n        var gaps = toNumbers(strokeDasharrayStyleProp.getString());\n\n        if (typeof ctx.setLineDash !== 'undefined') {\n          ctx.setLineDash(gaps);\n        } else // @ts-expect-error Handle browser prefix.\n          if (typeof ctx.webkitLineDash !== 'undefined') {\n            // @ts-expect-error Handle browser prefix.\n            ctx.webkitLineDash = gaps;\n          } else // @ts-expect-error Handle browser prefix.\n            if (typeof ctx.mozDash !== 'undefined' && !(gaps.length === 1 && gaps[0] === 0)) {\n              // @ts-expect-error Handle browser prefix.\n              ctx.mozDash = gaps;\n            }\n\n        var offset = strokeDashoffsetProp.getPixels();\n\n        if (typeof ctx.lineDashOffset !== 'undefined') {\n          ctx.lineDashOffset = offset;\n        } else // @ts-expect-error Handle browser prefix.\n          if (typeof ctx.webkitLineDashOffset !== 'undefined') {\n            // @ts-expect-error Handle browser prefix.\n            ctx.webkitLineDashOffset = offset;\n          } else // @ts-expect-error Handle browser prefix.\n            if (typeof ctx.mozDashOffset !== 'undefined') {\n              // @ts-expect-error Handle browser prefix.\n              ctx.mozDashOffset = offset;\n            }\n      }\n    } // font\n\n\n    this.modifiedEmSizeStack = false;\n\n    if (typeof ctx.font !== 'undefined') {\n      var fontStyleProp = this.getStyle('font');\n      var fontStyleStyleProp = this.getStyle('font-style');\n      var fontVariantStyleProp = this.getStyle('font-variant');\n      var fontWeightStyleProp = this.getStyle('font-weight');\n      var fontSizeStyleProp = this.getStyle('font-size');\n      var fontFamilyStyleProp = this.getStyle('font-family');\n      var font = new Font(fontStyleStyleProp.getString(), fontVariantStyleProp.getString(), fontWeightStyleProp.getString(), fontSizeStyleProp.hasValue() ? \"\".concat(fontSizeStyleProp.getPixels(true), \"px\") : '', fontFamilyStyleProp.getString(), Font.parse(fontStyleProp.getString(), ctx.font));\n      fontStyleStyleProp.setValue(font.fontStyle);\n      fontVariantStyleProp.setValue(font.fontVariant);\n      fontWeightStyleProp.setValue(font.fontWeight);\n      fontSizeStyleProp.setValue(font.fontSize);\n      fontFamilyStyleProp.setValue(font.fontFamily);\n      ctx.font = font.toString();\n\n      if (fontSizeStyleProp.isPixels()) {\n        this.document.emSize = fontSizeStyleProp.getPixels();\n        this.modifiedEmSizeStack = true;\n      }\n    }\n\n    if (!fromMeasure) {\n      // effects\n      this.applyEffects(ctx); // opacity\n\n      ctx.globalAlpha = this.calculateOpacity();\n    }\n  }\n\n  clearContext(ctx) {\n    super.clearContext(ctx);\n\n    if (this.modifiedEmSizeStack) {\n      this.document.popEmSize();\n    }\n  }\n\n}\n\nclass PathElement extends RenderedElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'path';\n    this.pathParser = null;\n    this.pathParser = new PathParser(this.getAttribute('d').getString());\n  }\n\n  path(ctx) {\n    var {\n      pathParser\n    } = this;\n    var boundingBox = new BoundingBox();\n    pathParser.reset();\n\n    if (ctx) {\n      ctx.beginPath();\n    }\n\n    while (!pathParser.isEnd()) {\n      switch (pathParser.next().type) {\n        case PathParser.MOVE_TO:\n          this.pathM(ctx, boundingBox);\n          break;\n\n        case PathParser.LINE_TO:\n          this.pathL(ctx, boundingBox);\n          break;\n\n        case PathParser.HORIZ_LINE_TO:\n          this.pathH(ctx, boundingBox);\n          break;\n\n        case PathParser.VERT_LINE_TO:\n          this.pathV(ctx, boundingBox);\n          break;\n\n        case PathParser.CURVE_TO:\n          this.pathC(ctx, boundingBox);\n          break;\n\n        case PathParser.SMOOTH_CURVE_TO:\n          this.pathS(ctx, boundingBox);\n          break;\n\n        case PathParser.QUAD_TO:\n          this.pathQ(ctx, boundingBox);\n          break;\n\n        case PathParser.SMOOTH_QUAD_TO:\n          this.pathT(ctx, boundingBox);\n          break;\n\n        case PathParser.ARC:\n          this.pathA(ctx, boundingBox);\n          break;\n\n        case PathParser.CLOSE_PATH:\n          this.pathZ(ctx, boundingBox);\n          break;\n      }\n    }\n\n    return boundingBox;\n  }\n\n  getBoundingBox(_) {\n    return this.path();\n  }\n\n  getMarkers() {\n    var {\n      pathParser\n    } = this;\n    var points = pathParser.getMarkerPoints();\n    var angles = pathParser.getMarkerAngles();\n    var markers = points.map((point, i) => [point, angles[i]]);\n    return markers;\n  }\n\n  renderChildren(ctx) {\n    this.path(ctx);\n    this.document.screen.mouse.checkPath(this, ctx);\n    var fillRuleStyleProp = this.getStyle('fill-rule');\n\n    if (ctx.fillStyle !== '') {\n      if (fillRuleStyleProp.getString('inherit') !== 'inherit') {\n        ctx.fill(fillRuleStyleProp.getString());\n      } else {\n        ctx.fill();\n      }\n    }\n\n    if (ctx.strokeStyle !== '') {\n      if (this.getAttribute('vector-effect').getString() === 'non-scaling-stroke') {\n        ctx.save();\n        ctx.setTransform(1, 0, 0, 1, 0, 0);\n        ctx.stroke();\n        ctx.restore();\n      } else {\n        ctx.stroke();\n      }\n    }\n\n    var markers = this.getMarkers();\n\n    if (markers) {\n      var markersLastIndex = markers.length - 1;\n      var markerStartStyleProp = this.getStyle('marker-start');\n      var markerMidStyleProp = this.getStyle('marker-mid');\n      var markerEndStyleProp = this.getStyle('marker-end');\n\n      if (markerStartStyleProp.isUrlDefinition()) {\n        var marker = markerStartStyleProp.getDefinition();\n        var [point, angle] = markers[0];\n        marker.render(ctx, point, angle);\n      }\n\n      if (markerMidStyleProp.isUrlDefinition()) {\n        var _marker = markerMidStyleProp.getDefinition();\n\n        for (var i = 1; i < markersLastIndex; i++) {\n          var [_point, _angle] = markers[i];\n\n          _marker.render(ctx, _point, _angle);\n        }\n      }\n\n      if (markerEndStyleProp.isUrlDefinition()) {\n        var _marker2 = markerEndStyleProp.getDefinition();\n\n        var [_point2, _angle2] = markers[markersLastIndex];\n\n        _marker2.render(ctx, _point2, _angle2);\n      }\n    }\n  }\n\n  static pathM(pathParser) {\n    var point = pathParser.getAsCurrentPoint();\n    pathParser.start = pathParser.current;\n    return {\n      point\n    };\n  }\n\n  pathM(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      point\n    } = PathElement.pathM(pathParser);\n    var {\n      x,\n      y\n    } = point;\n    pathParser.addMarker(point);\n    boundingBox.addPoint(x, y);\n\n    if (ctx) {\n      ctx.moveTo(x, y);\n    }\n  }\n\n  static pathL(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var point = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      point\n    };\n  }\n\n  pathL(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point\n    } = PathElement.pathL(pathParser);\n    var {\n      x,\n      y\n    } = point;\n    pathParser.addMarker(point, current);\n    boundingBox.addPoint(x, y);\n\n    if (ctx) {\n      ctx.lineTo(x, y);\n    }\n  }\n\n  static pathH(pathParser) {\n    var {\n      current,\n      command\n    } = pathParser;\n    var point = new Point((command.relative ? current.x : 0) + command.x, current.y);\n    pathParser.current = point;\n    return {\n      current,\n      point\n    };\n  }\n\n  pathH(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point\n    } = PathElement.pathH(pathParser);\n    var {\n      x,\n      y\n    } = point;\n    pathParser.addMarker(point, current);\n    boundingBox.addPoint(x, y);\n\n    if (ctx) {\n      ctx.lineTo(x, y);\n    }\n  }\n\n  static pathV(pathParser) {\n    var {\n      current,\n      command\n    } = pathParser;\n    var point = new Point(current.x, (command.relative ? current.y : 0) + command.y);\n    pathParser.current = point;\n    return {\n      current,\n      point\n    };\n  }\n\n  pathV(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point\n    } = PathElement.pathV(pathParser);\n    var {\n      x,\n      y\n    } = point;\n    pathParser.addMarker(point, current);\n    boundingBox.addPoint(x, y);\n\n    if (ctx) {\n      ctx.lineTo(x, y);\n    }\n  }\n\n  static pathC(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var point = pathParser.getPoint('x1', 'y1');\n    var controlPoint = pathParser.getAsControlPoint('x2', 'y2');\n    var currentPoint = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      point,\n      controlPoint,\n      currentPoint\n    };\n  }\n\n  pathC(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathC(pathParser);\n    pathParser.addMarker(currentPoint, controlPoint, point);\n    boundingBox.addBezierCurve(current.x, current.y, point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n\n    if (ctx) {\n      ctx.bezierCurveTo(point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    }\n  }\n\n  static pathS(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var point = pathParser.getReflectedControlPoint();\n    var controlPoint = pathParser.getAsControlPoint('x2', 'y2');\n    var currentPoint = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      point,\n      controlPoint,\n      currentPoint\n    };\n  }\n\n  pathS(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathS(pathParser);\n    pathParser.addMarker(currentPoint, controlPoint, point);\n    boundingBox.addBezierCurve(current.x, current.y, point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n\n    if (ctx) {\n      ctx.bezierCurveTo(point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    }\n  }\n\n  static pathQ(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var controlPoint = pathParser.getAsControlPoint('x1', 'y1');\n    var currentPoint = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      controlPoint,\n      currentPoint\n    };\n  }\n\n  pathQ(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathQ(pathParser);\n    pathParser.addMarker(currentPoint, controlPoint, controlPoint);\n    boundingBox.addQuadraticCurve(current.x, current.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n\n    if (ctx) {\n      ctx.quadraticCurveTo(controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    }\n  }\n\n  static pathT(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var controlPoint = pathParser.getReflectedControlPoint();\n    pathParser.control = controlPoint;\n    var currentPoint = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      controlPoint,\n      currentPoint\n    };\n  }\n\n  pathT(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathT(pathParser);\n    pathParser.addMarker(currentPoint, controlPoint, controlPoint);\n    boundingBox.addQuadraticCurve(current.x, current.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n\n    if (ctx) {\n      ctx.quadraticCurveTo(controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    }\n  }\n\n  static pathA(pathParser) {\n    var {\n      current,\n      command\n    } = pathParser;\n    var {\n      rX,\n      rY,\n      xRot,\n      lArcFlag,\n      sweepFlag\n    } = command;\n    var xAxisRotation = xRot * (Math.PI / 180.0);\n    var currentPoint = pathParser.getAsCurrentPoint(); // Conversion from endpoint to center parameterization\n    // http://www.w3.org/TR/SVG11/implnote.html#ArcImplementationNotes\n    // x1', y1'\n\n    var currp = new Point(Math.cos(xAxisRotation) * (current.x - currentPoint.x) / 2.0 + Math.sin(xAxisRotation) * (current.y - currentPoint.y) / 2.0, -Math.sin(xAxisRotation) * (current.x - currentPoint.x) / 2.0 + Math.cos(xAxisRotation) * (current.y - currentPoint.y) / 2.0); // adjust radii\n\n    var l = Math.pow(currp.x, 2) / Math.pow(rX, 2) + Math.pow(currp.y, 2) / Math.pow(rY, 2);\n\n    if (l > 1) {\n      rX *= Math.sqrt(l);\n      rY *= Math.sqrt(l);\n    } // cx', cy'\n\n\n    var s = (lArcFlag === sweepFlag ? -1 : 1) * Math.sqrt((Math.pow(rX, 2) * Math.pow(rY, 2) - Math.pow(rX, 2) * Math.pow(currp.y, 2) - Math.pow(rY, 2) * Math.pow(currp.x, 2)) / (Math.pow(rX, 2) * Math.pow(currp.y, 2) + Math.pow(rY, 2) * Math.pow(currp.x, 2)));\n\n    if (isNaN(s)) {\n      s = 0;\n    }\n\n    var cpp = new Point(s * rX * currp.y / rY, s * -rY * currp.x / rX); // cx, cy\n\n    var centp = new Point((current.x + currentPoint.x) / 2.0 + Math.cos(xAxisRotation) * cpp.x - Math.sin(xAxisRotation) * cpp.y, (current.y + currentPoint.y) / 2.0 + Math.sin(xAxisRotation) * cpp.x + Math.cos(xAxisRotation) * cpp.y); // initial angle\n\n    var a1 = vectorsAngle([1, 0], [(currp.x - cpp.x) / rX, (currp.y - cpp.y) / rY]); // θ1\n    // angle delta\n\n    var u = [(currp.x - cpp.x) / rX, (currp.y - cpp.y) / rY];\n    var v = [(-currp.x - cpp.x) / rX, (-currp.y - cpp.y) / rY];\n    var ad = vectorsAngle(u, v); // Δθ\n\n    if (vectorsRatio(u, v) <= -1) {\n      ad = Math.PI;\n    }\n\n    if (vectorsRatio(u, v) >= 1) {\n      ad = 0;\n    }\n\n    return {\n      currentPoint,\n      rX,\n      rY,\n      sweepFlag,\n      xAxisRotation,\n      centp,\n      a1,\n      ad\n    };\n  }\n\n  pathA(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      currentPoint,\n      rX,\n      rY,\n      sweepFlag,\n      xAxisRotation,\n      centp,\n      a1,\n      ad\n    } = PathElement.pathA(pathParser); // for markers\n\n    var dir = 1 - sweepFlag ? 1.0 : -1.0;\n    var ah = a1 + dir * (ad / 2.0);\n    var halfWay = new Point(centp.x + rX * Math.cos(ah), centp.y + rY * Math.sin(ah));\n    pathParser.addMarkerAngle(halfWay, ah - dir * Math.PI / 2);\n    pathParser.addMarkerAngle(currentPoint, ah - dir * Math.PI);\n    boundingBox.addPoint(currentPoint.x, currentPoint.y); // TODO: this is too naive, make it better\n\n    if (ctx && !isNaN(a1) && !isNaN(ad)) {\n      var r = rX > rY ? rX : rY;\n      var sx = rX > rY ? 1 : rX / rY;\n      var sy = rX > rY ? rY / rX : 1;\n      ctx.translate(centp.x, centp.y);\n      ctx.rotate(xAxisRotation);\n      ctx.scale(sx, sy);\n      ctx.arc(0, 0, r, a1, a1 + ad, Boolean(1 - sweepFlag));\n      ctx.scale(1 / sx, 1 / sy);\n      ctx.rotate(-xAxisRotation);\n      ctx.translate(-centp.x, -centp.y);\n    }\n  }\n\n  static pathZ(pathParser) {\n    pathParser.current = pathParser.start;\n  }\n\n  pathZ(ctx, boundingBox) {\n    PathElement.pathZ(this.pathParser);\n\n    if (ctx) {\n      // only close path if it is not a straight line\n      if (boundingBox.x1 !== boundingBox.x2 && boundingBox.y1 !== boundingBox.y2) {\n        ctx.closePath();\n      }\n    }\n  }\n\n}\n\nclass GlyphElement extends PathElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'glyph';\n    this.horizAdvX = this.getAttribute('horiz-adv-x').getNumber();\n    this.unicode = this.getAttribute('unicode').getString();\n    this.arabicForm = this.getAttribute('arabic-form').getString();\n  }\n\n}\n\nclass TextElement extends RenderedElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, new.target === TextElement ? true : captureTextNodes);\n    this.type = 'text';\n    this.x = 0;\n    this.y = 0;\n    this.measureCache = -1;\n  }\n\n  setContext(ctx) {\n    var fromMeasure = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    super.setContext(ctx, fromMeasure);\n    var textBaseline = this.getStyle('dominant-baseline').getTextBaseline() || this.getStyle('alignment-baseline').getTextBaseline();\n\n    if (textBaseline) {\n      ctx.textBaseline = textBaseline;\n    }\n  }\n\n  initializeCoordinates() {\n    this.x = 0;\n    this.y = 0;\n    this.leafTexts = [];\n    this.textChunkStart = 0;\n    this.minX = Number.POSITIVE_INFINITY;\n    this.maxX = Number.NEGATIVE_INFINITY;\n  }\n\n  getBoundingBox(ctx) {\n    if (this.type !== 'text') {\n      return this.getTElementBoundingBox(ctx);\n    } // first, calculate child positions\n\n\n    this.initializeCoordinates();\n    this.adjustChildCoordinatesRecursive(ctx);\n    var boundingBox = null; // then calculate bounding box\n\n    this.children.forEach((_, i) => {\n      var childBoundingBox = this.getChildBoundingBox(ctx, this, this, i);\n\n      if (!boundingBox) {\n        boundingBox = childBoundingBox;\n      } else {\n        boundingBox.addBoundingBox(childBoundingBox);\n      }\n    });\n    return boundingBox;\n  }\n\n  getFontSize() {\n    var {\n      document,\n      parent\n    } = this;\n    var inheritFontSize = Font.parse(document.ctx.font).fontSize;\n    var fontSize = parent.getStyle('font-size').getNumber(inheritFontSize);\n    return fontSize;\n  }\n\n  getTElementBoundingBox(ctx) {\n    var fontSize = this.getFontSize();\n    return new BoundingBox(this.x, this.y - fontSize, this.x + this.measureText(ctx), this.y);\n  }\n\n  getGlyph(font, text, i) {\n    var char = text[i];\n    var glyph = null;\n\n    if (font.isArabic) {\n      var len = text.length;\n      var prevChar = text[i - 1];\n      var nextChar = text[i + 1];\n      var arabicForm = 'isolated';\n\n      if ((i === 0 || prevChar === ' ') && i < len - 1 && nextChar !== ' ') {\n        arabicForm = 'terminal';\n      }\n\n      if (i > 0 && prevChar !== ' ' && i < len - 1 && nextChar !== ' ') {\n        arabicForm = 'medial';\n      }\n\n      if (i > 0 && prevChar !== ' ' && (i === len - 1 || nextChar === ' ')) {\n        arabicForm = 'initial';\n      }\n\n      if (typeof font.glyphs[char] !== 'undefined') {\n        // NEED TEST\n        var maybeGlyph = font.glyphs[char];\n        glyph = maybeGlyph instanceof GlyphElement ? maybeGlyph : maybeGlyph[arabicForm];\n      }\n    } else {\n      glyph = font.glyphs[char];\n    }\n\n    if (!glyph) {\n      glyph = font.missingGlyph;\n    }\n\n    return glyph;\n  }\n\n  getText() {\n    return '';\n  }\n\n  getTextFromNode(node) {\n    var textNode = node || this.node;\n    var childNodes = Array.from(textNode.parentNode.childNodes);\n    var index = childNodes.indexOf(textNode);\n    var lastIndex = childNodes.length - 1;\n    var text = compressSpaces( // textNode.value\n    // || textNode.text\n    textNode.textContent || '');\n\n    if (index === 0) {\n      text = trimLeft(text);\n    }\n\n    if (index === lastIndex) {\n      text = trimRight(text);\n    }\n\n    return text;\n  }\n\n  renderChildren(ctx) {\n    if (this.type !== 'text') {\n      this.renderTElementChildren(ctx);\n      return;\n    } // first, calculate child positions\n\n\n    this.initializeCoordinates();\n    this.adjustChildCoordinatesRecursive(ctx); // then render\n\n    this.children.forEach((_, i) => {\n      this.renderChild(ctx, this, this, i);\n    });\n    var {\n      mouse\n    } = this.document.screen; // Do not calc bounding box if mouse is not working.\n\n    if (mouse.isWorking()) {\n      mouse.checkBoundingBox(this, this.getBoundingBox(ctx));\n    }\n  }\n\n  renderTElementChildren(ctx) {\n    var {\n      document,\n      parent\n    } = this;\n    var renderText = this.getText();\n    var customFont = parent.getStyle('font-family').getDefinition();\n\n    if (customFont) {\n      var {\n        unitsPerEm\n      } = customFont.fontFace;\n      var ctxFont = Font.parse(document.ctx.font);\n      var fontSize = parent.getStyle('font-size').getNumber(ctxFont.fontSize);\n      var fontStyle = parent.getStyle('font-style').getString(ctxFont.fontStyle);\n      var scale = fontSize / unitsPerEm;\n      var text = customFont.isRTL ? renderText.split('').reverse().join('') : renderText;\n      var dx = toNumbers(parent.getAttribute('dx').getString());\n      var len = text.length;\n\n      for (var i = 0; i < len; i++) {\n        var glyph = this.getGlyph(customFont, text, i);\n        ctx.translate(this.x, this.y);\n        ctx.scale(scale, -scale);\n        var lw = ctx.lineWidth;\n        ctx.lineWidth = ctx.lineWidth * unitsPerEm / fontSize;\n\n        if (fontStyle === 'italic') {\n          ctx.transform(1, 0, .4, 1, 0, 0);\n        }\n\n        glyph.render(ctx);\n\n        if (fontStyle === 'italic') {\n          ctx.transform(1, 0, -.4, 1, 0, 0);\n        }\n\n        ctx.lineWidth = lw;\n        ctx.scale(1 / scale, -1 / scale);\n        ctx.translate(-this.x, -this.y);\n        this.x += fontSize * (glyph.horizAdvX || customFont.horizAdvX) / unitsPerEm;\n\n        if (typeof dx[i] !== 'undefined' && !isNaN(dx[i])) {\n          this.x += dx[i];\n        }\n      }\n\n      return;\n    }\n\n    var {\n      x,\n      y\n    } = this; // NEED TEST\n    // if (ctx.paintOrder === 'stroke') {\n    // \tif (ctx.strokeStyle) {\n    // \t\tctx.strokeText(renderText, x, y);\n    // \t}\n    // \tif (ctx.fillStyle) {\n    // \t\tctx.fillText(renderText, x, y);\n    // \t}\n    // } else {\n\n    if (ctx.fillStyle) {\n      ctx.fillText(renderText, x, y);\n    }\n\n    if (ctx.strokeStyle) {\n      ctx.strokeText(renderText, x, y);\n    } // }\n\n  }\n\n  applyAnchoring() {\n    if (this.textChunkStart >= this.leafTexts.length) {\n      return;\n    } // This is basically the \"Apply anchoring\" part of https://www.w3.org/TR/SVG2/text.html#TextLayoutAlgorithm.\n    // The difference is that we apply the anchoring as soon as a chunk is finished. This saves some extra looping.\n    // Vertical text is not supported.\n\n\n    var firstElement = this.leafTexts[this.textChunkStart];\n    var textAnchor = firstElement.getStyle('text-anchor').getString('start');\n    var isRTL = false; // we treat RTL like LTR\n\n    var shift = 0;\n\n    if (textAnchor === 'start' && !isRTL || textAnchor === 'end' && isRTL) {\n      shift = firstElement.x - this.minX;\n    } else if (textAnchor === 'end' && !isRTL || textAnchor === 'start' && isRTL) {\n      shift = firstElement.x - this.maxX;\n    } else {\n      shift = firstElement.x - (this.minX + this.maxX) / 2;\n    }\n\n    for (var i = this.textChunkStart; i < this.leafTexts.length; i++) {\n      this.leafTexts[i].x += shift;\n    } // start new chunk\n\n\n    this.minX = Number.POSITIVE_INFINITY;\n    this.maxX = Number.NEGATIVE_INFINITY;\n    this.textChunkStart = this.leafTexts.length;\n  }\n\n  adjustChildCoordinatesRecursive(ctx) {\n    this.children.forEach((_, i) => {\n      this.adjustChildCoordinatesRecursiveCore(ctx, this, this, i);\n    });\n    this.applyAnchoring();\n  }\n\n  adjustChildCoordinatesRecursiveCore(ctx, textParent, parent, i) {\n    var child = parent.children[i];\n\n    if (child.children.length > 0) {\n      child.children.forEach((_, i) => {\n        textParent.adjustChildCoordinatesRecursiveCore(ctx, textParent, child, i);\n      });\n    } else {\n      // only leafs are relevant\n      this.adjustChildCoordinates(ctx, textParent, parent, i);\n    }\n  }\n\n  adjustChildCoordinates(ctx, textParent, parent, i) {\n    var child = parent.children[i];\n\n    if (typeof child.measureText !== 'function') {\n      return child;\n    }\n\n    ctx.save();\n    child.setContext(ctx, true);\n    var xAttr = child.getAttribute('x');\n    var yAttr = child.getAttribute('y');\n    var dxAttr = child.getAttribute('dx');\n    var dyAttr = child.getAttribute('dy');\n    var customFont = child.getStyle('font-family').getDefinition();\n    var isRTL = Boolean(customFont) && customFont.isRTL;\n\n    if (i === 0) {\n      // First children inherit attributes from parent(s). Positional attributes\n      // are only inherited from a parent to it's first child.\n      if (!xAttr.hasValue()) {\n        xAttr.setValue(child.getInheritedAttribute('x'));\n      }\n\n      if (!yAttr.hasValue()) {\n        yAttr.setValue(child.getInheritedAttribute('y'));\n      }\n\n      if (!dxAttr.hasValue()) {\n        dxAttr.setValue(child.getInheritedAttribute('dx'));\n      }\n\n      if (!dyAttr.hasValue()) {\n        dyAttr.setValue(child.getInheritedAttribute('dy'));\n      }\n    }\n\n    var width = child.measureText(ctx);\n\n    if (isRTL) {\n      textParent.x -= width;\n    }\n\n    if (xAttr.hasValue()) {\n      // an \"x\" attribute marks the start of a new chunk\n      textParent.applyAnchoring();\n      child.x = xAttr.getPixels('x');\n\n      if (dxAttr.hasValue()) {\n        child.x += dxAttr.getPixels('x');\n      }\n    } else {\n      if (dxAttr.hasValue()) {\n        textParent.x += dxAttr.getPixels('x');\n      }\n\n      child.x = textParent.x;\n    }\n\n    textParent.x = child.x;\n\n    if (!isRTL) {\n      textParent.x += width;\n    }\n\n    if (yAttr.hasValue()) {\n      child.y = yAttr.getPixels('y');\n\n      if (dyAttr.hasValue()) {\n        child.y += dyAttr.getPixels('y');\n      }\n    } else {\n      if (dyAttr.hasValue()) {\n        textParent.y += dyAttr.getPixels('y');\n      }\n\n      child.y = textParent.y;\n    }\n\n    textParent.y = child.y; // update the current chunk and it's bounds\n\n    textParent.leafTexts.push(child);\n    textParent.minX = Math.min(textParent.minX, child.x, child.x + width);\n    textParent.maxX = Math.max(textParent.maxX, child.x, child.x + width);\n    child.clearContext(ctx);\n    ctx.restore();\n    return child;\n  }\n\n  getChildBoundingBox(ctx, textParent, parent, i) {\n    var child = parent.children[i]; // not a text node?\n\n    if (typeof child.getBoundingBox !== 'function') {\n      return null;\n    }\n\n    var boundingBox = child.getBoundingBox(ctx);\n\n    if (!boundingBox) {\n      return null;\n    }\n\n    child.children.forEach((_, i) => {\n      var childBoundingBox = textParent.getChildBoundingBox(ctx, textParent, child, i);\n      boundingBox.addBoundingBox(childBoundingBox);\n    });\n    return boundingBox;\n  }\n\n  renderChild(ctx, textParent, parent, i) {\n    var child = parent.children[i];\n    child.render(ctx);\n    child.children.forEach((_, i) => {\n      textParent.renderChild(ctx, textParent, child, i);\n    });\n  }\n\n  measureText(ctx) {\n    var {\n      measureCache\n    } = this;\n\n    if (~measureCache) {\n      return measureCache;\n    }\n\n    var renderText = this.getText();\n    var measure = this.measureTargetText(ctx, renderText);\n    this.measureCache = measure;\n    return measure;\n  }\n\n  measureTargetText(ctx, targetText) {\n    if (!targetText.length) {\n      return 0;\n    }\n\n    var {\n      parent\n    } = this;\n    var customFont = parent.getStyle('font-family').getDefinition();\n\n    if (customFont) {\n      var fontSize = this.getFontSize();\n      var text = customFont.isRTL ? targetText.split('').reverse().join('') : targetText;\n      var dx = toNumbers(parent.getAttribute('dx').getString());\n      var len = text.length;\n      var _measure = 0;\n\n      for (var i = 0; i < len; i++) {\n        var glyph = this.getGlyph(customFont, text, i);\n        _measure += (glyph.horizAdvX || customFont.horizAdvX) * fontSize / customFont.fontFace.unitsPerEm;\n\n        if (typeof dx[i] !== 'undefined' && !isNaN(dx[i])) {\n          _measure += dx[i];\n        }\n      }\n\n      return _measure;\n    }\n\n    if (!ctx.measureText) {\n      return targetText.length * 10;\n    }\n\n    ctx.save();\n    this.setContext(ctx, true);\n    var {\n      width: measure\n    } = ctx.measureText(targetText);\n    this.clearContext(ctx);\n    ctx.restore();\n    return measure;\n  }\n  /**\r\n   * Inherits positional attributes from {@link TextElement} parent(s). Attributes\r\n   * are only inherited from a parent to its first child.\r\n   * @param name - The attribute name.\r\n   * @returns The attribute value or null.\r\n   */\n\n\n  getInheritedAttribute(name) {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias,consistent-this\n    var current = this;\n\n    while (current instanceof TextElement && current.isFirstChild()) {\n      var parentAttr = current.parent.getAttribute(name);\n\n      if (parentAttr.hasValue(true)) {\n        return parentAttr.getValue('0');\n      }\n\n      current = current.parent;\n    }\n\n    return null;\n  }\n\n}\n\nclass TSpanElement extends TextElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, new.target === TSpanElement ? true : captureTextNodes);\n    this.type = 'tspan'; // if this node has children, then they own the text\n\n    this.text = this.children.length > 0 ? '' : this.getTextFromNode();\n  }\n\n  getText() {\n    return this.text;\n  }\n\n}\n\nclass TextNode extends TSpanElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'textNode';\n  }\n\n}\n\nclass SVGElement extends RenderedElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'svg';\n    this.root = false;\n  }\n\n  setContext(ctx) {\n    var _this$node$parentNode;\n\n    var {\n      document\n    } = this;\n    var {\n      screen,\n      window\n    } = document;\n    var canvas = ctx.canvas;\n    screen.setDefaults(ctx);\n\n    if (canvas.style && typeof ctx.font !== 'undefined' && window && typeof window.getComputedStyle !== 'undefined') {\n      ctx.font = window.getComputedStyle(canvas).getPropertyValue('font');\n      var fontSizeProp = new Property(document, 'fontSize', Font.parse(ctx.font).fontSize);\n\n      if (fontSizeProp.hasValue()) {\n        document.rootEmSize = fontSizeProp.getPixels('y');\n        document.emSize = document.rootEmSize;\n      }\n    } // create new view port\n\n\n    if (!this.getAttribute('x').hasValue()) {\n      this.getAttribute('x', true).setValue(0);\n    }\n\n    if (!this.getAttribute('y').hasValue()) {\n      this.getAttribute('y', true).setValue(0);\n    }\n\n    var {\n      width,\n      height\n    } = screen.viewPort;\n\n    if (!this.getStyle('width').hasValue()) {\n      this.getStyle('width', true).setValue('100%');\n    }\n\n    if (!this.getStyle('height').hasValue()) {\n      this.getStyle('height', true).setValue('100%');\n    }\n\n    if (!this.getStyle('color').hasValue()) {\n      this.getStyle('color', true).setValue('black');\n    }\n\n    var refXAttr = this.getAttribute('refX');\n    var refYAttr = this.getAttribute('refY');\n    var viewBoxAttr = this.getAttribute('viewBox');\n    var viewBox = viewBoxAttr.hasValue() ? toNumbers(viewBoxAttr.getString()) : null;\n    var clip = !this.root && this.getStyle('overflow').getValue('hidden') !== 'visible';\n    var minX = 0;\n    var minY = 0;\n    var clipX = 0;\n    var clipY = 0;\n\n    if (viewBox) {\n      minX = viewBox[0];\n      minY = viewBox[1];\n    }\n\n    if (!this.root) {\n      width = this.getStyle('width').getPixels('x');\n      height = this.getStyle('height').getPixels('y');\n\n      if (this.type === 'marker') {\n        clipX = minX;\n        clipY = minY;\n        minX = 0;\n        minY = 0;\n      }\n    }\n\n    screen.viewPort.setCurrent(width, height); // Default value of transform-origin is center only for root SVG elements\n    // https://developer.mozilla.org/en-US/docs/Web/SVG/Attribute/transform-origin\n\n    if (this.node // is not temporary SVGElement\n    && (!this.parent || ((_this$node$parentNode = this.node.parentNode) === null || _this$node$parentNode === void 0 ? void 0 : _this$node$parentNode.nodeName) === 'foreignObject') && this.getStyle('transform', false, true).hasValue() && !this.getStyle('transform-origin', false, true).hasValue()) {\n      this.getStyle('transform-origin', true, true).setValue('50% 50%');\n    }\n\n    super.setContext(ctx);\n    ctx.translate(this.getAttribute('x').getPixels('x'), this.getAttribute('y').getPixels('y'));\n\n    if (viewBox) {\n      width = viewBox[2];\n      height = viewBox[3];\n    }\n\n    document.setViewBox({\n      ctx,\n      aspectRatio: this.getAttribute('preserveAspectRatio').getString(),\n      width: screen.viewPort.width,\n      desiredWidth: width,\n      height: screen.viewPort.height,\n      desiredHeight: height,\n      minX,\n      minY,\n      refX: refXAttr.getValue(),\n      refY: refYAttr.getValue(),\n      clip,\n      clipX,\n      clipY\n    });\n\n    if (viewBox) {\n      screen.viewPort.removeCurrent();\n      screen.viewPort.setCurrent(width, height);\n    }\n  }\n\n  clearContext(ctx) {\n    super.clearContext(ctx);\n    this.document.screen.viewPort.removeCurrent();\n  }\n  /**\r\n   * Resize SVG to fit in given size.\r\n   * @param width\r\n   * @param height\r\n   * @param preserveAspectRatio\r\n   */\n\n\n  resize(width) {\n    var height = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : width;\n    var preserveAspectRatio = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    var widthAttr = this.getAttribute('width', true);\n    var heightAttr = this.getAttribute('height', true);\n    var viewBoxAttr = this.getAttribute('viewBox');\n    var styleAttr = this.getAttribute('style');\n    var originWidth = widthAttr.getNumber(0);\n    var originHeight = heightAttr.getNumber(0);\n\n    if (preserveAspectRatio) {\n      if (typeof preserveAspectRatio === 'string') {\n        this.getAttribute('preserveAspectRatio', true).setValue(preserveAspectRatio);\n      } else {\n        var preserveAspectRatioAttr = this.getAttribute('preserveAspectRatio');\n\n        if (preserveAspectRatioAttr.hasValue()) {\n          preserveAspectRatioAttr.setValue(preserveAspectRatioAttr.getString().replace(/^\\s*(\\S.*\\S)\\s*$/, '$1'));\n        }\n      }\n    }\n\n    widthAttr.setValue(width);\n    heightAttr.setValue(height);\n\n    if (!viewBoxAttr.hasValue()) {\n      viewBoxAttr.setValue(\"0 0 \".concat(originWidth || width, \" \").concat(originHeight || height));\n    }\n\n    if (styleAttr.hasValue()) {\n      var widthStyle = this.getStyle('width');\n      var heightStyle = this.getStyle('height');\n\n      if (widthStyle.hasValue()) {\n        widthStyle.setValue(\"\".concat(width, \"px\"));\n      }\n\n      if (heightStyle.hasValue()) {\n        heightStyle.setValue(\"\".concat(height, \"px\"));\n      }\n    }\n  }\n\n}\n\nclass RectElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'rect';\n  }\n\n  path(ctx) {\n    var x = this.getAttribute('x').getPixels('x');\n    var y = this.getAttribute('y').getPixels('y');\n    var width = this.getStyle('width', false, true).getPixels('x');\n    var height = this.getStyle('height', false, true).getPixels('y');\n    var rxAttr = this.getAttribute('rx');\n    var ryAttr = this.getAttribute('ry');\n    var rx = rxAttr.getPixels('x');\n    var ry = ryAttr.getPixels('y');\n\n    if (rxAttr.hasValue() && !ryAttr.hasValue()) {\n      ry = rx;\n    }\n\n    if (ryAttr.hasValue() && !rxAttr.hasValue()) {\n      rx = ry;\n    }\n\n    rx = Math.min(rx, width / 2.0);\n    ry = Math.min(ry, height / 2.0);\n\n    if (ctx) {\n      var KAPPA = 4 * ((Math.sqrt(2) - 1) / 3);\n      ctx.beginPath(); // always start the path so we don't fill prior paths\n\n      if (height > 0 && width > 0) {\n        ctx.moveTo(x + rx, y);\n        ctx.lineTo(x + width - rx, y);\n        ctx.bezierCurveTo(x + width - rx + KAPPA * rx, y, x + width, y + ry - KAPPA * ry, x + width, y + ry);\n        ctx.lineTo(x + width, y + height - ry);\n        ctx.bezierCurveTo(x + width, y + height - ry + KAPPA * ry, x + width - rx + KAPPA * rx, y + height, x + width - rx, y + height);\n        ctx.lineTo(x + rx, y + height);\n        ctx.bezierCurveTo(x + rx - KAPPA * rx, y + height, x, y + height - ry + KAPPA * ry, x, y + height - ry);\n        ctx.lineTo(x, y + ry);\n        ctx.bezierCurveTo(x, y + ry - KAPPA * ry, x + rx - KAPPA * rx, y, x + rx, y);\n        ctx.closePath();\n      }\n    }\n\n    return new BoundingBox(x, y, x + width, y + height);\n  }\n\n  getMarkers() {\n    return null;\n  }\n\n}\n\nclass CircleElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'circle';\n  }\n\n  path(ctx) {\n    var cx = this.getAttribute('cx').getPixels('x');\n    var cy = this.getAttribute('cy').getPixels('y');\n    var r = this.getAttribute('r').getPixels();\n\n    if (ctx && r > 0) {\n      ctx.beginPath();\n      ctx.arc(cx, cy, r, 0, Math.PI * 2, false);\n      ctx.closePath();\n    }\n\n    return new BoundingBox(cx - r, cy - r, cx + r, cy + r);\n  }\n\n  getMarkers() {\n    return null;\n  }\n\n}\n\nclass EllipseElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'ellipse';\n  }\n\n  path(ctx) {\n    var KAPPA = 4 * ((Math.sqrt(2) - 1) / 3);\n    var rx = this.getAttribute('rx').getPixels('x');\n    var ry = this.getAttribute('ry').getPixels('y');\n    var cx = this.getAttribute('cx').getPixels('x');\n    var cy = this.getAttribute('cy').getPixels('y');\n\n    if (ctx && rx > 0 && ry > 0) {\n      ctx.beginPath();\n      ctx.moveTo(cx + rx, cy);\n      ctx.bezierCurveTo(cx + rx, cy + KAPPA * ry, cx + KAPPA * rx, cy + ry, cx, cy + ry);\n      ctx.bezierCurveTo(cx - KAPPA * rx, cy + ry, cx - rx, cy + KAPPA * ry, cx - rx, cy);\n      ctx.bezierCurveTo(cx - rx, cy - KAPPA * ry, cx - KAPPA * rx, cy - ry, cx, cy - ry);\n      ctx.bezierCurveTo(cx + KAPPA * rx, cy - ry, cx + rx, cy - KAPPA * ry, cx + rx, cy);\n      ctx.closePath();\n    }\n\n    return new BoundingBox(cx - rx, cy - ry, cx + rx, cy + ry);\n  }\n\n  getMarkers() {\n    return null;\n  }\n\n}\n\nclass LineElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'line';\n  }\n\n  getPoints() {\n    return [new Point(this.getAttribute('x1').getPixels('x'), this.getAttribute('y1').getPixels('y')), new Point(this.getAttribute('x2').getPixels('x'), this.getAttribute('y2').getPixels('y'))];\n  }\n\n  path(ctx) {\n    var [{\n      x: x0,\n      y: y0\n    }, {\n      x: x1,\n      y: y1\n    }] = this.getPoints();\n\n    if (ctx) {\n      ctx.beginPath();\n      ctx.moveTo(x0, y0);\n      ctx.lineTo(x1, y1);\n    }\n\n    return new BoundingBox(x0, y0, x1, y1);\n  }\n\n  getMarkers() {\n    var [p0, p1] = this.getPoints();\n    var a = p0.angleTo(p1);\n    return [[p0, a], [p1, a]];\n  }\n\n}\n\nclass PolylineElement extends PathElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'polyline';\n    this.points = [];\n    this.points = Point.parsePath(this.getAttribute('points').getString());\n  }\n\n  path(ctx) {\n    var {\n      points\n    } = this;\n    var [{\n      x: x0,\n      y: y0\n    }] = points;\n    var boundingBox = new BoundingBox(x0, y0);\n\n    if (ctx) {\n      ctx.beginPath();\n      ctx.moveTo(x0, y0);\n    }\n\n    points.forEach(_ref => {\n      var {\n        x,\n        y\n      } = _ref;\n      boundingBox.addPoint(x, y);\n\n      if (ctx) {\n        ctx.lineTo(x, y);\n      }\n    });\n    return boundingBox;\n  }\n\n  getMarkers() {\n    var {\n      points\n    } = this;\n    var lastIndex = points.length - 1;\n    var markers = [];\n    points.forEach((point, i) => {\n      if (i === lastIndex) {\n        return;\n      }\n\n      markers.push([point, point.angleTo(points[i + 1])]);\n    });\n\n    if (markers.length > 0) {\n      markers.push([points[points.length - 1], markers[markers.length - 1][1]]);\n    }\n\n    return markers;\n  }\n\n}\n\nclass PolygonElement extends PolylineElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'polygon';\n  }\n\n  path(ctx) {\n    var boundingBox = super.path(ctx);\n    var [{\n      x,\n      y\n    }] = this.points;\n\n    if (ctx) {\n      ctx.lineTo(x, y);\n      ctx.closePath();\n    }\n\n    return boundingBox;\n  }\n\n}\n\nclass PatternElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'pattern';\n  }\n\n  createPattern(ctx, _, parentOpacityProp) {\n    var width = this.getStyle('width').getPixels('x', true);\n    var height = this.getStyle('height').getPixels('y', true); // render me using a temporary svg element\n\n    var patternSvg = new SVGElement(this.document, null);\n    patternSvg.attributes.viewBox = new Property(this.document, 'viewBox', this.getAttribute('viewBox').getValue());\n    patternSvg.attributes.width = new Property(this.document, 'width', \"\".concat(width, \"px\"));\n    patternSvg.attributes.height = new Property(this.document, 'height', \"\".concat(height, \"px\"));\n    patternSvg.attributes.transform = new Property(this.document, 'transform', this.getAttribute('patternTransform').getValue());\n    patternSvg.children = this.children;\n    var patternCanvas = this.document.createCanvas(width, height);\n    var patternCtx = patternCanvas.getContext('2d');\n    var xAttr = this.getAttribute('x');\n    var yAttr = this.getAttribute('y');\n\n    if (xAttr.hasValue() && yAttr.hasValue()) {\n      patternCtx.translate(xAttr.getPixels('x', true), yAttr.getPixels('y', true));\n    }\n\n    if (parentOpacityProp.hasValue()) {\n      this.styles['fill-opacity'] = parentOpacityProp;\n    } else {\n      Reflect.deleteProperty(this.styles, 'fill-opacity');\n    } // render 3x3 grid so when we transform there's no white space on edges\n\n\n    for (var x = -1; x <= 1; x++) {\n      for (var y = -1; y <= 1; y++) {\n        patternCtx.save();\n        patternSvg.attributes.x = new Property(this.document, 'x', x * patternCanvas.width);\n        patternSvg.attributes.y = new Property(this.document, 'y', y * patternCanvas.height);\n        patternSvg.render(patternCtx);\n        patternCtx.restore();\n      }\n    }\n\n    var pattern = ctx.createPattern(patternCanvas, 'repeat');\n    return pattern;\n  }\n\n}\n\nclass MarkerElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'marker';\n  }\n\n  render(ctx, point, angle) {\n    if (!point) {\n      return;\n    }\n\n    var {\n      x,\n      y\n    } = point;\n    var orient = this.getAttribute('orient').getString('auto');\n    var markerUnits = this.getAttribute('markerUnits').getString('strokeWidth');\n    ctx.translate(x, y);\n\n    if (orient === 'auto') {\n      ctx.rotate(angle);\n    }\n\n    if (markerUnits === 'strokeWidth') {\n      ctx.scale(ctx.lineWidth, ctx.lineWidth);\n    }\n\n    ctx.save(); // render me using a temporary svg element\n\n    var markerSvg = new SVGElement(this.document, null);\n    markerSvg.type = this.type;\n    markerSvg.attributes.viewBox = new Property(this.document, 'viewBox', this.getAttribute('viewBox').getValue());\n    markerSvg.attributes.refX = new Property(this.document, 'refX', this.getAttribute('refX').getValue());\n    markerSvg.attributes.refY = new Property(this.document, 'refY', this.getAttribute('refY').getValue());\n    markerSvg.attributes.width = new Property(this.document, 'width', this.getAttribute('markerWidth').getValue());\n    markerSvg.attributes.height = new Property(this.document, 'height', this.getAttribute('markerHeight').getValue());\n    markerSvg.attributes.overflow = new Property(this.document, 'overflow', this.getAttribute('overflow').getValue());\n    markerSvg.attributes.fill = new Property(this.document, 'fill', this.getAttribute('fill').getColor('black'));\n    markerSvg.attributes.stroke = new Property(this.document, 'stroke', this.getAttribute('stroke').getValue('none'));\n    markerSvg.children = this.children;\n    markerSvg.render(ctx);\n    ctx.restore();\n\n    if (markerUnits === 'strokeWidth') {\n      ctx.scale(1 / ctx.lineWidth, 1 / ctx.lineWidth);\n    }\n\n    if (orient === 'auto') {\n      ctx.rotate(-angle);\n    }\n\n    ctx.translate(-x, -y);\n  }\n\n}\n\nclass DefsElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'defs';\n  }\n\n  render() {// NOOP\n  }\n\n}\n\nclass GElement extends RenderedElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'g';\n  }\n\n  getBoundingBox(ctx) {\n    var boundingBox = new BoundingBox();\n    this.children.forEach(child => {\n      boundingBox.addBoundingBox(child.getBoundingBox(ctx));\n    });\n    return boundingBox;\n  }\n\n}\n\nclass GradientElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.attributesToInherit = ['gradientUnits'];\n    this.stops = [];\n    var {\n      stops,\n      children\n    } = this;\n    children.forEach(child => {\n      if (child.type === 'stop') {\n        stops.push(child);\n      }\n    });\n  }\n\n  getGradientUnits() {\n    return this.getAttribute('gradientUnits').getString('objectBoundingBox');\n  }\n\n  createGradient(ctx, element, parentOpacityProp) {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias, consistent-this\n    var stopsContainer = this;\n\n    if (this.getHrefAttribute().hasValue()) {\n      stopsContainer = this.getHrefAttribute().getDefinition();\n      this.inheritStopContainer(stopsContainer);\n    }\n\n    var {\n      stops\n    } = stopsContainer;\n    var gradient = this.getGradient(ctx, element);\n\n    if (!gradient) {\n      return this.addParentOpacity(parentOpacityProp, stops[stops.length - 1].color);\n    }\n\n    stops.forEach(stop => {\n      gradient.addColorStop(stop.offset, this.addParentOpacity(parentOpacityProp, stop.color));\n    });\n\n    if (this.getAttribute('gradientTransform').hasValue()) {\n      // render as transformed pattern on temporary canvas\n      var {\n        document\n      } = this;\n      var {\n        MAX_VIRTUAL_PIXELS,\n        viewPort\n      } = document.screen;\n      var [rootView] = viewPort.viewPorts;\n      var rect = new RectElement(document, null);\n      rect.attributes.x = new Property(document, 'x', -MAX_VIRTUAL_PIXELS / 3.0);\n      rect.attributes.y = new Property(document, 'y', -MAX_VIRTUAL_PIXELS / 3.0);\n      rect.attributes.width = new Property(document, 'width', MAX_VIRTUAL_PIXELS);\n      rect.attributes.height = new Property(document, 'height', MAX_VIRTUAL_PIXELS);\n      var group = new GElement(document, null);\n      group.attributes.transform = new Property(document, 'transform', this.getAttribute('gradientTransform').getValue());\n      group.children = [rect];\n      var patternSvg = new SVGElement(document, null);\n      patternSvg.attributes.x = new Property(document, 'x', 0);\n      patternSvg.attributes.y = new Property(document, 'y', 0);\n      patternSvg.attributes.width = new Property(document, 'width', rootView.width);\n      patternSvg.attributes.height = new Property(document, 'height', rootView.height);\n      patternSvg.children = [group];\n      var patternCanvas = document.createCanvas(rootView.width, rootView.height);\n      var patternCtx = patternCanvas.getContext('2d');\n      patternCtx.fillStyle = gradient;\n      patternSvg.render(patternCtx);\n      return patternCtx.createPattern(patternCanvas, 'no-repeat');\n    }\n\n    return gradient;\n  }\n\n  inheritStopContainer(stopsContainer) {\n    this.attributesToInherit.forEach(attributeToInherit => {\n      if (!this.getAttribute(attributeToInherit).hasValue() && stopsContainer.getAttribute(attributeToInherit).hasValue()) {\n        this.getAttribute(attributeToInherit, true).setValue(stopsContainer.getAttribute(attributeToInherit).getValue());\n      }\n    });\n  }\n\n  addParentOpacity(parentOpacityProp, color) {\n    if (parentOpacityProp.hasValue()) {\n      var colorProp = new Property(this.document, 'color', color);\n      return colorProp.addOpacity(parentOpacityProp).getColor();\n    }\n\n    return color;\n  }\n\n}\n\nclass LinearGradientElement extends GradientElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'linearGradient';\n    this.attributesToInherit.push('x1', 'y1', 'x2', 'y2');\n  }\n\n  getGradient(ctx, element) {\n    var isBoundingBoxUnits = this.getGradientUnits() === 'objectBoundingBox';\n    var boundingBox = isBoundingBoxUnits ? element.getBoundingBox(ctx) : null;\n\n    if (isBoundingBoxUnits && !boundingBox) {\n      return null;\n    }\n\n    if (!this.getAttribute('x1').hasValue() && !this.getAttribute('y1').hasValue() && !this.getAttribute('x2').hasValue() && !this.getAttribute('y2').hasValue()) {\n      this.getAttribute('x1', true).setValue(0);\n      this.getAttribute('y1', true).setValue(0);\n      this.getAttribute('x2', true).setValue(1);\n      this.getAttribute('y2', true).setValue(0);\n    }\n\n    var x1 = isBoundingBoxUnits ? boundingBox.x + boundingBox.width * this.getAttribute('x1').getNumber() : this.getAttribute('x1').getPixels('x');\n    var y1 = isBoundingBoxUnits ? boundingBox.y + boundingBox.height * this.getAttribute('y1').getNumber() : this.getAttribute('y1').getPixels('y');\n    var x2 = isBoundingBoxUnits ? boundingBox.x + boundingBox.width * this.getAttribute('x2').getNumber() : this.getAttribute('x2').getPixels('x');\n    var y2 = isBoundingBoxUnits ? boundingBox.y + boundingBox.height * this.getAttribute('y2').getNumber() : this.getAttribute('y2').getPixels('y');\n\n    if (x1 === x2 && y1 === y2) {\n      return null;\n    }\n\n    return ctx.createLinearGradient(x1, y1, x2, y2);\n  }\n\n}\n\nclass RadialGradientElement extends GradientElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'radialGradient';\n    this.attributesToInherit.push('cx', 'cy', 'r', 'fx', 'fy', 'fr');\n  }\n\n  getGradient(ctx, element) {\n    var isBoundingBoxUnits = this.getGradientUnits() === 'objectBoundingBox';\n    var boundingBox = element.getBoundingBox(ctx);\n\n    if (isBoundingBoxUnits && !boundingBox) {\n      return null;\n    }\n\n    if (!this.getAttribute('cx').hasValue()) {\n      this.getAttribute('cx', true).setValue('50%');\n    }\n\n    if (!this.getAttribute('cy').hasValue()) {\n      this.getAttribute('cy', true).setValue('50%');\n    }\n\n    if (!this.getAttribute('r').hasValue()) {\n      this.getAttribute('r', true).setValue('50%');\n    }\n\n    var cx = isBoundingBoxUnits ? boundingBox.x + boundingBox.width * this.getAttribute('cx').getNumber() : this.getAttribute('cx').getPixels('x');\n    var cy = isBoundingBoxUnits ? boundingBox.y + boundingBox.height * this.getAttribute('cy').getNumber() : this.getAttribute('cy').getPixels('y');\n    var fx = cx;\n    var fy = cy;\n\n    if (this.getAttribute('fx').hasValue()) {\n      fx = isBoundingBoxUnits ? boundingBox.x + boundingBox.width * this.getAttribute('fx').getNumber() : this.getAttribute('fx').getPixels('x');\n    }\n\n    if (this.getAttribute('fy').hasValue()) {\n      fy = isBoundingBoxUnits ? boundingBox.y + boundingBox.height * this.getAttribute('fy').getNumber() : this.getAttribute('fy').getPixels('y');\n    }\n\n    var r = isBoundingBoxUnits ? (boundingBox.width + boundingBox.height) / 2.0 * this.getAttribute('r').getNumber() : this.getAttribute('r').getPixels();\n    var fr = this.getAttribute('fr').getPixels();\n    return ctx.createRadialGradient(fx, fy, fr, cx, cy, r);\n  }\n\n}\n\nclass StopElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'stop';\n    var offset = Math.max(0, Math.min(1, this.getAttribute('offset').getNumber()));\n    var stopOpacity = this.getStyle('stop-opacity');\n    var stopColor = this.getStyle('stop-color', true);\n\n    if (stopColor.getString() === '') {\n      stopColor.setValue('#000');\n    }\n\n    if (stopOpacity.hasValue()) {\n      stopColor = stopColor.addOpacity(stopOpacity);\n    }\n\n    this.offset = offset;\n    this.color = stopColor.getColor();\n  }\n\n}\n\nclass AnimateElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'animate';\n    this.duration = 0;\n    this.initialValue = null;\n    this.initialUnits = '';\n    this.removed = false;\n    this.frozen = false;\n    document.screen.animations.push(this);\n    this.begin = this.getAttribute('begin').getMilliseconds();\n    this.maxDuration = this.begin + this.getAttribute('dur').getMilliseconds();\n    this.from = this.getAttribute('from');\n    this.to = this.getAttribute('to');\n    this.values = new Property(document, 'values', null);\n    var valuesAttr = this.getAttribute('values');\n\n    if (valuesAttr.hasValue()) {\n      this.values.setValue(valuesAttr.getString().split(';'));\n    }\n  }\n\n  getProperty() {\n    var attributeType = this.getAttribute('attributeType').getString();\n    var attributeName = this.getAttribute('attributeName').getString();\n\n    if (attributeType === 'CSS') {\n      return this.parent.getStyle(attributeName, true);\n    }\n\n    return this.parent.getAttribute(attributeName, true);\n  }\n\n  calcValue() {\n    var {\n      initialUnits\n    } = this;\n    var {\n      progress,\n      from,\n      to\n    } = this.getProgress(); // tween value linearly\n\n    var newValue = from.getNumber() + (to.getNumber() - from.getNumber()) * progress;\n\n    if (initialUnits === '%') {\n      newValue *= 100.0; // numValue() returns 0-1 whereas properties are 0-100\n    }\n\n    return \"\".concat(newValue).concat(initialUnits);\n  }\n\n  update(delta) {\n    var {\n      parent\n    } = this;\n    var prop = this.getProperty(); // set initial value\n\n    if (!this.initialValue) {\n      this.initialValue = prop.getString();\n      this.initialUnits = prop.getUnits();\n    } // if we're past the end time\n\n\n    if (this.duration > this.maxDuration) {\n      var fill = this.getAttribute('fill').getString('remove'); // loop for indefinitely repeating animations\n\n      if (this.getAttribute('repeatCount').getString() === 'indefinite' || this.getAttribute('repeatDur').getString() === 'indefinite') {\n        this.duration = 0;\n      } else if (fill === 'freeze' && !this.frozen) {\n        this.frozen = true;\n        parent.animationFrozen = true;\n        parent.animationFrozenValue = prop.getString();\n      } else if (fill === 'remove' && !this.removed) {\n        this.removed = true;\n        prop.setValue(parent.animationFrozen ? parent.animationFrozenValue : this.initialValue);\n        return true;\n      }\n\n      return false;\n    }\n\n    this.duration += delta; // if we're past the begin time\n\n    var updated = false;\n\n    if (this.begin < this.duration) {\n      var newValue = this.calcValue(); // tween\n\n      var typeAttr = this.getAttribute('type');\n\n      if (typeAttr.hasValue()) {\n        // for transform, etc.\n        var type = typeAttr.getString();\n        newValue = \"\".concat(type, \"(\").concat(newValue, \")\");\n      }\n\n      prop.setValue(newValue);\n      updated = true;\n    }\n\n    return updated;\n  }\n\n  getProgress() {\n    var {\n      document,\n      values\n    } = this;\n    var result = {\n      progress: (this.duration - this.begin) / (this.maxDuration - this.begin)\n    };\n\n    if (values.hasValue()) {\n      var p = result.progress * (values.getValue().length - 1);\n      var lb = Math.floor(p);\n      var ub = Math.ceil(p);\n      result.from = new Property(document, 'from', parseFloat(values.getValue()[lb]));\n      result.to = new Property(document, 'to', parseFloat(values.getValue()[ub]));\n      result.progress = (p - lb) / (ub - lb);\n    } else {\n      result.from = this.from;\n      result.to = this.to;\n    }\n\n    return result;\n  }\n\n}\n\nclass AnimateColorElement extends AnimateElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'animateColor';\n  }\n\n  calcValue() {\n    var {\n      progress,\n      from,\n      to\n    } = this.getProgress();\n    var colorFrom = new rgbcolor__WEBPACK_IMPORTED_MODULE_13__(from.getColor());\n    var colorTo = new rgbcolor__WEBPACK_IMPORTED_MODULE_13__(to.getColor());\n\n    if (colorFrom.ok && colorTo.ok) {\n      // tween color linearly\n      var r = colorFrom.r + (colorTo.r - colorFrom.r) * progress;\n      var g = colorFrom.g + (colorTo.g - colorFrom.g) * progress;\n      var b = colorFrom.b + (colorTo.b - colorFrom.b) * progress; // ? alpha\n\n      return \"rgb(\".concat(Math.floor(r), \", \").concat(Math.floor(g), \", \").concat(Math.floor(b), \")\");\n    }\n\n    return this.getAttribute('from').getColor();\n  }\n\n}\n\nclass AnimateTransformElement extends AnimateElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'animateTransform';\n  }\n\n  calcValue() {\n    var {\n      progress,\n      from,\n      to\n    } = this.getProgress(); // tween value linearly\n\n    var transformFrom = toNumbers(from.getString());\n    var transformTo = toNumbers(to.getString());\n    var newValue = transformFrom.map((from, i) => {\n      var to = transformTo[i];\n      return from + (to - from) * progress;\n    }).join(' ');\n    return newValue;\n  }\n\n}\n\nclass FontElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'font';\n    this.glyphs = Object.create(null);\n    this.horizAdvX = this.getAttribute('horiz-adv-x').getNumber();\n    var {\n      definitions\n    } = document;\n    var {\n      children\n    } = this;\n\n    for (var child of children) {\n      switch (child.type) {\n        case 'font-face':\n          {\n            this.fontFace = child;\n            var fontFamilyStyle = child.getStyle('font-family');\n\n            if (fontFamilyStyle.hasValue()) {\n              definitions[fontFamilyStyle.getString()] = this;\n            }\n\n            break;\n          }\n\n        case 'missing-glyph':\n          this.missingGlyph = child;\n          break;\n\n        case 'glyph':\n          {\n            var glyph = child;\n\n            if (glyph.arabicForm) {\n              this.isRTL = true;\n              this.isArabic = true;\n\n              if (typeof this.glyphs[glyph.unicode] === 'undefined') {\n                this.glyphs[glyph.unicode] = Object.create(null);\n              }\n\n              this.glyphs[glyph.unicode][glyph.arabicForm] = glyph;\n            } else {\n              this.glyphs[glyph.unicode] = glyph;\n            }\n\n            break;\n          }\n      }\n    }\n  }\n\n  render() {// NO RENDER\n  }\n\n}\n\nclass FontFaceElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'font-face';\n    this.ascent = this.getAttribute('ascent').getNumber();\n    this.descent = this.getAttribute('descent').getNumber();\n    this.unitsPerEm = this.getAttribute('units-per-em').getNumber();\n  }\n\n}\n\nclass MissingGlyphElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'missing-glyph';\n    this.horizAdvX = 0;\n  }\n\n}\n\nclass TRefElement extends TextElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'tref';\n  }\n\n  getText() {\n    var element = this.getHrefAttribute().getDefinition();\n\n    if (element) {\n      var firstChild = element.children[0];\n\n      if (firstChild) {\n        return firstChild.getText();\n      }\n    }\n\n    return '';\n  }\n\n}\n\nclass AElement extends TextElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'a';\n    var {\n      childNodes\n    } = node;\n    var firstChild = childNodes[0];\n    var hasText = childNodes.length > 0 && Array.from(childNodes).every(node => node.nodeType === 3);\n    this.hasText = hasText;\n    this.text = hasText ? this.getTextFromNode(firstChild) : '';\n  }\n\n  getText() {\n    return this.text;\n  }\n\n  renderChildren(ctx) {\n    if (this.hasText) {\n      // render as text element\n      super.renderChildren(ctx);\n      var {\n        document,\n        x,\n        y\n      } = this;\n      var {\n        mouse\n      } = document.screen;\n      var fontSize = new Property(document, 'fontSize', Font.parse(document.ctx.font).fontSize); // Do not calc bounding box if mouse is not working.\n\n      if (mouse.isWorking()) {\n        mouse.checkBoundingBox(this, new BoundingBox(x, y - fontSize.getPixels('y'), x + this.measureText(ctx), y));\n      }\n    } else if (this.children.length > 0) {\n      // render as temporary group\n      var g = new GElement(this.document, null);\n      g.children = this.children;\n      g.parent = this;\n      g.render(ctx);\n    }\n  }\n\n  onClick() {\n    var {\n      window\n    } = this.document;\n\n    if (window) {\n      window.open(this.getHrefAttribute().getString());\n    }\n  }\n\n  onMouseMove() {\n    var ctx = this.document.ctx;\n    ctx.canvas.style.cursor = 'pointer';\n  }\n\n}\n\nfunction ownKeys$2(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread$2(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys$2(Object(source), true).forEach(function (key) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_7__(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys$2(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\nclass TextPathElement extends TextElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'textPath';\n    this.textWidth = 0;\n    this.textHeight = 0;\n    this.pathLength = -1;\n    this.glyphInfo = null;\n    this.letterSpacingCache = [];\n    this.measuresCache = new Map([['', 0]]);\n    var pathElement = this.getHrefAttribute().getDefinition();\n    this.text = this.getTextFromNode();\n    this.dataArray = this.parsePathData(pathElement);\n  }\n\n  getText() {\n    return this.text;\n  }\n\n  path(ctx) {\n    var {\n      dataArray\n    } = this;\n\n    if (ctx) {\n      ctx.beginPath();\n    }\n\n    dataArray.forEach(_ref => {\n      var {\n        type,\n        points\n      } = _ref;\n\n      switch (type) {\n        case PathParser.LINE_TO:\n          if (ctx) {\n            ctx.lineTo(points[0], points[1]);\n          }\n\n          break;\n\n        case PathParser.MOVE_TO:\n          if (ctx) {\n            ctx.moveTo(points[0], points[1]);\n          }\n\n          break;\n\n        case PathParser.CURVE_TO:\n          if (ctx) {\n            ctx.bezierCurveTo(points[0], points[1], points[2], points[3], points[4], points[5]);\n          }\n\n          break;\n\n        case PathParser.QUAD_TO:\n          if (ctx) {\n            ctx.quadraticCurveTo(points[0], points[1], points[2], points[3]);\n          }\n\n          break;\n\n        case PathParser.ARC:\n          {\n            var [cx, cy, rx, ry, theta, dTheta, psi, fs] = points;\n            var r = rx > ry ? rx : ry;\n            var scaleX = rx > ry ? 1 : rx / ry;\n            var scaleY = rx > ry ? ry / rx : 1;\n\n            if (ctx) {\n              ctx.translate(cx, cy);\n              ctx.rotate(psi);\n              ctx.scale(scaleX, scaleY);\n              ctx.arc(0, 0, r, theta, theta + dTheta, Boolean(1 - fs));\n              ctx.scale(1 / scaleX, 1 / scaleY);\n              ctx.rotate(-psi);\n              ctx.translate(-cx, -cy);\n            }\n\n            break;\n          }\n\n        case PathParser.CLOSE_PATH:\n          if (ctx) {\n            ctx.closePath();\n          }\n\n          break;\n      }\n    });\n  }\n\n  renderChildren(ctx) {\n    this.setTextData(ctx);\n    ctx.save();\n    var textDecoration = this.parent.getStyle('text-decoration').getString();\n    var fontSize = this.getFontSize();\n    var {\n      glyphInfo\n    } = this;\n    var fill = ctx.fillStyle;\n\n    if (textDecoration === 'underline') {\n      ctx.beginPath();\n    }\n\n    glyphInfo.forEach((glyph, i) => {\n      var {\n        p0,\n        p1,\n        rotation,\n        text: partialText\n      } = glyph;\n      ctx.save();\n      ctx.translate(p0.x, p0.y);\n      ctx.rotate(rotation);\n\n      if (ctx.fillStyle) {\n        ctx.fillText(partialText, 0, 0);\n      }\n\n      if (ctx.strokeStyle) {\n        ctx.strokeText(partialText, 0, 0);\n      }\n\n      ctx.restore();\n\n      if (textDecoration === 'underline') {\n        if (i === 0) {\n          ctx.moveTo(p0.x, p0.y + fontSize / 8);\n        }\n\n        ctx.lineTo(p1.x, p1.y + fontSize / 5);\n      } // // To assist with debugging visually, uncomment following\n      //\n      // ctx.beginPath();\n      // if (i % 2)\n      // \tctx.strokeStyle = 'red';\n      // else\n      // \tctx.strokeStyle = 'green';\n      // ctx.moveTo(p0.x, p0.y);\n      // ctx.lineTo(p1.x, p1.y);\n      // ctx.stroke();\n      // ctx.closePath();\n\n    });\n\n    if (textDecoration === 'underline') {\n      ctx.lineWidth = fontSize / 20;\n      ctx.strokeStyle = fill;\n      ctx.stroke();\n      ctx.closePath();\n    }\n\n    ctx.restore();\n  }\n\n  getLetterSpacingAt() {\n    var idx = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n    return this.letterSpacingCache[idx] || 0;\n  }\n\n  findSegmentToFitChar(ctx, anchor, textFullWidth, fullPathWidth, spacesNumber, inputOffset, dy, c, charI) {\n    var offset = inputOffset;\n    var glyphWidth = this.measureText(ctx, c);\n\n    if (c === ' ' && anchor === 'justify' && textFullWidth < fullPathWidth) {\n      glyphWidth += (fullPathWidth - textFullWidth) / spacesNumber;\n    }\n\n    if (charI > -1) {\n      offset += this.getLetterSpacingAt(charI);\n    }\n\n    var splineStep = this.textHeight / 20;\n    var p0 = this.getEquidistantPointOnPath(offset, splineStep, 0);\n    var p1 = this.getEquidistantPointOnPath(offset + glyphWidth, splineStep, 0);\n    var segment = {\n      p0,\n      p1\n    };\n    var rotation = p0 && p1 ? Math.atan2(p1.y - p0.y, p1.x - p0.x) : 0;\n\n    if (dy) {\n      var dyX = Math.cos(Math.PI / 2 + rotation) * dy;\n      var dyY = Math.cos(-rotation) * dy;\n      segment.p0 = _objectSpread$2(_objectSpread$2({}, p0), {}, {\n        x: p0.x + dyX,\n        y: p0.y + dyY\n      });\n      segment.p1 = _objectSpread$2(_objectSpread$2({}, p1), {}, {\n        x: p1.x + dyX,\n        y: p1.y + dyY\n      });\n    }\n\n    offset += glyphWidth;\n    return {\n      offset,\n      segment,\n      rotation\n    };\n  }\n\n  measureText(ctx, text) {\n    var {\n      measuresCache\n    } = this;\n    var targetText = text || this.getText();\n\n    if (measuresCache.has(targetText)) {\n      return measuresCache.get(targetText);\n    }\n\n    var measure = this.measureTargetText(ctx, targetText);\n    measuresCache.set(targetText, measure);\n    return measure;\n  } // This method supposes what all custom fonts already loaded.\n  // If some font will be loaded after this method call, <textPath> will not be rendered correctly.\n  // You need to call this method manually to update glyphs cache.\n\n\n  setTextData(ctx) {\n    if (this.glyphInfo) {\n      return;\n    }\n\n    var renderText = this.getText();\n    var chars = renderText.split('');\n    var spacesNumber = renderText.split(' ').length - 1;\n    var dx = this.parent.getAttribute('dx').split().map(_ => _.getPixels('x'));\n    var dy = this.parent.getAttribute('dy').getPixels('y');\n    var anchor = this.parent.getStyle('text-anchor').getString('start');\n    var thisSpacing = this.getStyle('letter-spacing');\n    var parentSpacing = this.parent.getStyle('letter-spacing');\n    var letterSpacing = 0;\n\n    if (!thisSpacing.hasValue() || thisSpacing.getValue() === 'inherit') {\n      letterSpacing = parentSpacing.getPixels();\n    } else if (thisSpacing.hasValue()) {\n      if (thisSpacing.getValue() !== 'initial' && thisSpacing.getValue() !== 'unset') {\n        letterSpacing = thisSpacing.getPixels();\n      }\n    } // fill letter-spacing cache\n\n\n    var letterSpacingCache = [];\n    var textLen = renderText.length;\n    this.letterSpacingCache = letterSpacingCache;\n\n    for (var i = 0; i < textLen; i++) {\n      letterSpacingCache.push(typeof dx[i] !== 'undefined' ? dx[i] : letterSpacing);\n    }\n\n    var dxSum = letterSpacingCache.reduce((acc, cur, i) => i === 0 ? 0 : acc + cur || 0, 0);\n    var textWidth = this.measureText(ctx);\n    var textFullWidth = Math.max(textWidth + dxSum, 0);\n    this.textWidth = textWidth;\n    this.textHeight = this.getFontSize();\n    this.glyphInfo = [];\n    var fullPathWidth = this.getPathLength();\n    var startOffset = this.getStyle('startOffset').getNumber(0) * fullPathWidth;\n    var offset = 0;\n\n    if (anchor === 'middle' || anchor === 'center') {\n      offset = -textFullWidth / 2;\n    }\n\n    if (anchor === 'end' || anchor === 'right') {\n      offset = -textFullWidth;\n    }\n\n    offset += startOffset;\n    chars.forEach((char, i) => {\n      // Find such segment what distance between p0 and p1 is approx. width of glyph\n      var {\n        offset: nextOffset,\n        segment,\n        rotation\n      } = this.findSegmentToFitChar(ctx, anchor, textFullWidth, fullPathWidth, spacesNumber, offset, dy, char, i);\n      offset = nextOffset;\n\n      if (!segment.p0 || !segment.p1) {\n        return;\n      } // const width = this.getLineLength(\n      // \tsegment.p0.x,\n      // \tsegment.p0.y,\n      // \tsegment.p1.x,\n      // \tsegment.p1.y\n      // );\n      // Note: Since glyphs are rendered one at a time, any kerning pair data built into the font will not be used.\n      // Can foresee having a rough pair table built in that the developer can override as needed.\n      // Or use \"dx\" attribute of the <text> node as a naive replacement\n      // const kern = 0;\n      // placeholder for future implementation\n      // const midpoint = this.getPointOnLine(\n      // \tkern + width / 2.0,\n      // \tsegment.p0.x, segment.p0.y, segment.p1.x, segment.p1.y\n      // );\n\n\n      this.glyphInfo.push({\n        // transposeX: midpoint.x,\n        // transposeY: midpoint.y,\n        text: chars[i],\n        p0: segment.p0,\n        p1: segment.p1,\n        rotation\n      });\n    });\n  }\n\n  parsePathData(path) {\n    this.pathLength = -1; // reset path length\n\n    if (!path) {\n      return [];\n    }\n\n    var pathCommands = [];\n    var {\n      pathParser\n    } = path;\n    pathParser.reset(); // convert l, H, h, V, and v to L\n\n    while (!pathParser.isEnd()) {\n      var {\n        current\n      } = pathParser;\n      var startX = current ? current.x : 0;\n      var startY = current ? current.y : 0;\n      var command = pathParser.next();\n      var nextCommandType = command.type;\n      var points = [];\n\n      switch (command.type) {\n        case PathParser.MOVE_TO:\n          this.pathM(pathParser, points);\n          break;\n\n        case PathParser.LINE_TO:\n          nextCommandType = this.pathL(pathParser, points);\n          break;\n\n        case PathParser.HORIZ_LINE_TO:\n          nextCommandType = this.pathH(pathParser, points);\n          break;\n\n        case PathParser.VERT_LINE_TO:\n          nextCommandType = this.pathV(pathParser, points);\n          break;\n\n        case PathParser.CURVE_TO:\n          this.pathC(pathParser, points);\n          break;\n\n        case PathParser.SMOOTH_CURVE_TO:\n          nextCommandType = this.pathS(pathParser, points);\n          break;\n\n        case PathParser.QUAD_TO:\n          this.pathQ(pathParser, points);\n          break;\n\n        case PathParser.SMOOTH_QUAD_TO:\n          nextCommandType = this.pathT(pathParser, points);\n          break;\n\n        case PathParser.ARC:\n          points = this.pathA(pathParser);\n          break;\n\n        case PathParser.CLOSE_PATH:\n          PathElement.pathZ(pathParser);\n          break;\n      }\n\n      if (command.type !== PathParser.CLOSE_PATH) {\n        pathCommands.push({\n          type: nextCommandType,\n          points,\n          start: {\n            x: startX,\n            y: startY\n          },\n          pathLength: this.calcLength(startX, startY, nextCommandType, points)\n        });\n      } else {\n        pathCommands.push({\n          type: PathParser.CLOSE_PATH,\n          points: [],\n          pathLength: 0\n        });\n      }\n    }\n\n    return pathCommands;\n  }\n\n  pathM(pathParser, points) {\n    var {\n      x,\n      y\n    } = PathElement.pathM(pathParser).point;\n    points.push(x, y);\n  }\n\n  pathL(pathParser, points) {\n    var {\n      x,\n      y\n    } = PathElement.pathL(pathParser).point;\n    points.push(x, y);\n    return PathParser.LINE_TO;\n  }\n\n  pathH(pathParser, points) {\n    var {\n      x,\n      y\n    } = PathElement.pathH(pathParser).point;\n    points.push(x, y);\n    return PathParser.LINE_TO;\n  }\n\n  pathV(pathParser, points) {\n    var {\n      x,\n      y\n    } = PathElement.pathV(pathParser).point;\n    points.push(x, y);\n    return PathParser.LINE_TO;\n  }\n\n  pathC(pathParser, points) {\n    var {\n      point,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathC(pathParser);\n    points.push(point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n  }\n\n  pathS(pathParser, points) {\n    var {\n      point,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathS(pathParser);\n    points.push(point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    return PathParser.CURVE_TO;\n  }\n\n  pathQ(pathParser, points) {\n    var {\n      controlPoint,\n      currentPoint\n    } = PathElement.pathQ(pathParser);\n    points.push(controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n  }\n\n  pathT(pathParser, points) {\n    var {\n      controlPoint,\n      currentPoint\n    } = PathElement.pathT(pathParser);\n    points.push(controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    return PathParser.QUAD_TO;\n  }\n\n  pathA(pathParser) {\n    var {\n      rX,\n      rY,\n      sweepFlag,\n      xAxisRotation,\n      centp,\n      a1,\n      ad\n    } = PathElement.pathA(pathParser);\n\n    if (sweepFlag === 0 && ad > 0) {\n      ad -= 2 * Math.PI;\n    }\n\n    if (sweepFlag === 1 && ad < 0) {\n      ad += 2 * Math.PI;\n    }\n\n    return [centp.x, centp.y, rX, rY, a1, ad, xAxisRotation, sweepFlag];\n  }\n\n  calcLength(x, y, commandType, points) {\n    var len = 0;\n    var p1 = null;\n    var p2 = null;\n    var t = 0;\n\n    switch (commandType) {\n      case PathParser.LINE_TO:\n        return this.getLineLength(x, y, points[0], points[1]);\n\n      case PathParser.CURVE_TO:\n        // Approximates by breaking curve into 100 line segments\n        len = 0.0;\n        p1 = this.getPointOnCubicBezier(0, x, y, points[0], points[1], points[2], points[3], points[4], points[5]);\n\n        for (t = 0.01; t <= 1; t += 0.01) {\n          p2 = this.getPointOnCubicBezier(t, x, y, points[0], points[1], points[2], points[3], points[4], points[5]);\n          len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n          p1 = p2;\n        }\n\n        return len;\n\n      case PathParser.QUAD_TO:\n        // Approximates by breaking curve into 100 line segments\n        len = 0.0;\n        p1 = this.getPointOnQuadraticBezier(0, x, y, points[0], points[1], points[2], points[3]);\n\n        for (t = 0.01; t <= 1; t += 0.01) {\n          p2 = this.getPointOnQuadraticBezier(t, x, y, points[0], points[1], points[2], points[3]);\n          len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n          p1 = p2;\n        }\n\n        return len;\n\n      case PathParser.ARC:\n        {\n          // Approximates by breaking curve into line segments\n          len = 0.0;\n          var start = points[4]; // 4 = theta\n\n          var dTheta = points[5]; // 5 = dTheta\n\n          var end = points[4] + dTheta;\n          var inc = Math.PI / 180.0; // 1 degree resolution\n\n          if (Math.abs(start - end) < inc) {\n            inc = Math.abs(start - end);\n          } // Note: for purpose of calculating arc length, not going to worry about rotating X-axis by angle psi\n\n\n          p1 = this.getPointOnEllipticalArc(points[0], points[1], points[2], points[3], start, 0);\n\n          if (dTheta < 0) {\n            // clockwise\n            for (t = start - inc; t > end; t -= inc) {\n              p2 = this.getPointOnEllipticalArc(points[0], points[1], points[2], points[3], t, 0);\n              len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n              p1 = p2;\n            }\n          } else {\n            // counter-clockwise\n            for (t = start + inc; t < end; t += inc) {\n              p2 = this.getPointOnEllipticalArc(points[0], points[1], points[2], points[3], t, 0);\n              len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n              p1 = p2;\n            }\n          }\n\n          p2 = this.getPointOnEllipticalArc(points[0], points[1], points[2], points[3], end, 0);\n          len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n          return len;\n        }\n    }\n\n    return 0;\n  }\n\n  getPointOnLine(dist, p1x, p1y, p2x, p2y) {\n    var fromX = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : p1x;\n    var fromY = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : p1y;\n    var m = (p2y - p1y) / (p2x - p1x + PSEUDO_ZERO);\n    var run = Math.sqrt(dist * dist / (1 + m * m));\n\n    if (p2x < p1x) {\n      run *= -1;\n    }\n\n    var rise = m * run;\n    var pt = null;\n\n    if (p2x === p1x) {\n      // vertical line\n      pt = {\n        x: fromX,\n        y: fromY + rise\n      };\n    } else if ((fromY - p1y) / (fromX - p1x + PSEUDO_ZERO) === m) {\n      pt = {\n        x: fromX + run,\n        y: fromY + rise\n      };\n    } else {\n      var ix = 0;\n      var iy = 0;\n      var len = this.getLineLength(p1x, p1y, p2x, p2y);\n\n      if (len < PSEUDO_ZERO) {\n        return null;\n      }\n\n      var u = (fromX - p1x) * (p2x - p1x) + (fromY - p1y) * (p2y - p1y);\n      u /= len * len;\n      ix = p1x + u * (p2x - p1x);\n      iy = p1y + u * (p2y - p1y);\n      var pRise = this.getLineLength(fromX, fromY, ix, iy);\n      var pRun = Math.sqrt(dist * dist - pRise * pRise);\n      run = Math.sqrt(pRun * pRun / (1 + m * m));\n\n      if (p2x < p1x) {\n        run *= -1;\n      }\n\n      rise = m * run;\n      pt = {\n        x: ix + run,\n        y: iy + rise\n      };\n    }\n\n    return pt;\n  }\n\n  getPointOnPath(distance) {\n    var fullLen = this.getPathLength();\n    var cumulativePathLength = 0;\n    var p = null;\n\n    if (distance < -0.00005 || distance - 0.00005 > fullLen) {\n      return null;\n    }\n\n    var {\n      dataArray\n    } = this;\n\n    for (var command of dataArray) {\n      if (command && (command.pathLength < 0.00005 || cumulativePathLength + command.pathLength + 0.00005 < distance)) {\n        cumulativePathLength += command.pathLength;\n        continue;\n      }\n\n      var delta = distance - cumulativePathLength;\n      var currentT = 0;\n\n      switch (command.type) {\n        case PathParser.LINE_TO:\n          p = this.getPointOnLine(delta, command.start.x, command.start.y, command.points[0], command.points[1], command.start.x, command.start.y);\n          break;\n\n        case PathParser.ARC:\n          {\n            var start = command.points[4]; // 4 = theta\n\n            var dTheta = command.points[5]; // 5 = dTheta\n\n            var end = command.points[4] + dTheta;\n            currentT = start + delta / command.pathLength * dTheta;\n\n            if (dTheta < 0 && currentT < end || dTheta >= 0 && currentT > end) {\n              break;\n            }\n\n            p = this.getPointOnEllipticalArc(command.points[0], command.points[1], command.points[2], command.points[3], currentT, command.points[6]);\n            break;\n          }\n\n        case PathParser.CURVE_TO:\n          currentT = delta / command.pathLength;\n\n          if (currentT > 1) {\n            currentT = 1;\n          }\n\n          p = this.getPointOnCubicBezier(currentT, command.start.x, command.start.y, command.points[0], command.points[1], command.points[2], command.points[3], command.points[4], command.points[5]);\n          break;\n\n        case PathParser.QUAD_TO:\n          currentT = delta / command.pathLength;\n\n          if (currentT > 1) {\n            currentT = 1;\n          }\n\n          p = this.getPointOnQuadraticBezier(currentT, command.start.x, command.start.y, command.points[0], command.points[1], command.points[2], command.points[3]);\n          break;\n      }\n\n      if (p) {\n        return p;\n      }\n\n      break;\n    }\n\n    return null;\n  }\n\n  getLineLength(x1, y1, x2, y2) {\n    return Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));\n  }\n\n  getPathLength() {\n    if (this.pathLength === -1) {\n      this.pathLength = this.dataArray.reduce((length, command) => command.pathLength > 0 ? length + command.pathLength : length, 0);\n    }\n\n    return this.pathLength;\n  }\n\n  getPointOnCubicBezier(pct, p1x, p1y, p2x, p2y, p3x, p3y, p4x, p4y) {\n    var x = p4x * CB1(pct) + p3x * CB2(pct) + p2x * CB3(pct) + p1x * CB4(pct);\n    var y = p4y * CB1(pct) + p3y * CB2(pct) + p2y * CB3(pct) + p1y * CB4(pct);\n    return {\n      x,\n      y\n    };\n  }\n\n  getPointOnQuadraticBezier(pct, p1x, p1y, p2x, p2y, p3x, p3y) {\n    var x = p3x * QB1(pct) + p2x * QB2(pct) + p1x * QB3(pct);\n    var y = p3y * QB1(pct) + p2y * QB2(pct) + p1y * QB3(pct);\n    return {\n      x,\n      y\n    };\n  }\n\n  getPointOnEllipticalArc(cx, cy, rx, ry, theta, psi) {\n    var cosPsi = Math.cos(psi);\n    var sinPsi = Math.sin(psi);\n    var pt = {\n      x: rx * Math.cos(theta),\n      y: ry * Math.sin(theta)\n    };\n    return {\n      x: cx + (pt.x * cosPsi - pt.y * sinPsi),\n      y: cy + (pt.x * sinPsi + pt.y * cosPsi)\n    };\n  } // TODO need some optimisations. possibly build cache only for curved segments?\n\n\n  buildEquidistantCache(inputStep, inputPrecision) {\n    var fullLen = this.getPathLength();\n    var precision = inputPrecision || 0.25; // accuracy vs performance\n\n    var step = inputStep || fullLen / 100;\n\n    if (!this.equidistantCache || this.equidistantCache.step !== step || this.equidistantCache.precision !== precision) {\n      // Prepare cache\n      this.equidistantCache = {\n        step,\n        precision,\n        points: []\n      }; // Calculate points\n\n      var s = 0;\n\n      for (var l = 0; l <= fullLen; l += precision) {\n        var p0 = this.getPointOnPath(l);\n        var p1 = this.getPointOnPath(l + precision);\n\n        if (!p0 || !p1) {\n          continue;\n        }\n\n        s += this.getLineLength(p0.x, p0.y, p1.x, p1.y);\n\n        if (s >= step) {\n          this.equidistantCache.points.push({\n            x: p0.x,\n            y: p0.y,\n            distance: l\n          });\n          s -= step;\n        }\n      }\n    }\n  }\n\n  getEquidistantPointOnPath(targetDistance, step, precision) {\n    this.buildEquidistantCache(step, precision);\n\n    if (targetDistance < 0 || targetDistance - this.getPathLength() > 0.00005) {\n      return null;\n    }\n\n    var idx = Math.round(targetDistance / this.getPathLength() * (this.equidistantCache.points.length - 1));\n    return this.equidistantCache.points[idx] || null;\n  }\n\n}\n\nvar dataUriRegex = /^\\s*data:(([^/,;]+\\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;\nclass ImageElement extends RenderedElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'image';\n    this.loaded = false;\n    var href = this.getHrefAttribute().getString();\n\n    if (!href) {\n      return;\n    }\n\n    var isSvg = href.endsWith('.svg') || /^\\s*data:image\\/svg\\+xml/i.test(href);\n    document.images.push(this);\n\n    if (!isSvg) {\n      void this.loadImage(href);\n    } else {\n      void this.loadSvg(href);\n    }\n\n    this.isSvg = isSvg;\n  }\n\n  loadImage(href) {\n    var _this = this;\n\n    return _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* () {\n      try {\n        var image = yield _this.document.createImage(href);\n        _this.image = image;\n      } catch (err) {\n        console.error(\"Error while loading image \\\"\".concat(href, \"\\\":\"), err);\n      }\n\n      _this.loaded = true;\n    })();\n  }\n\n  loadSvg(href) {\n    var _this2 = this;\n\n    return _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* () {\n      var match = dataUriRegex.exec(href);\n\n      if (match) {\n        var data = match[5];\n\n        if (match[4] === 'base64') {\n          _this2.image = atob(data);\n        } else {\n          _this2.image = decodeURIComponent(data);\n        }\n      } else {\n        try {\n          var response = yield _this2.document.fetch(href);\n          var svg = yield response.text();\n          _this2.image = svg;\n        } catch (err) {\n          console.error(\"Error while loading image \\\"\".concat(href, \"\\\":\"), err);\n        }\n      }\n\n      _this2.loaded = true;\n    })();\n  }\n\n  renderChildren(ctx) {\n    var {\n      document,\n      image,\n      loaded\n    } = this;\n    var x = this.getAttribute('x').getPixels('x');\n    var y = this.getAttribute('y').getPixels('y');\n    var width = this.getStyle('width').getPixels('x');\n    var height = this.getStyle('height').getPixels('y');\n\n    if (!loaded || !image || !width || !height) {\n      return;\n    }\n\n    ctx.save();\n    ctx.translate(x, y);\n\n    if (this.isSvg) {\n      var subDocument = document.canvg.forkString(ctx, this.image, {\n        ignoreMouse: true,\n        ignoreAnimation: true,\n        ignoreDimensions: true,\n        ignoreClear: true,\n        offsetX: 0,\n        offsetY: 0,\n        scaleWidth: width,\n        scaleHeight: height\n      });\n      subDocument.document.documentElement.parent = this;\n      void subDocument.render();\n    } else {\n      var _image = this.image;\n      document.setViewBox({\n        ctx,\n        aspectRatio: this.getAttribute('preserveAspectRatio').getString(),\n        width,\n        desiredWidth: _image.width,\n        height,\n        desiredHeight: _image.height\n      });\n\n      if (this.loaded) {\n        if (typeof _image.complete === 'undefined' || _image.complete) {\n          ctx.drawImage(_image, 0, 0);\n        }\n      }\n    }\n\n    ctx.restore();\n  }\n\n  getBoundingBox() {\n    var x = this.getAttribute('x').getPixels('x');\n    var y = this.getAttribute('y').getPixels('y');\n    var width = this.getStyle('width').getPixels('x');\n    var height = this.getStyle('height').getPixels('y');\n    return new BoundingBox(x, y, x + width, y + height);\n  }\n\n}\n\nclass SymbolElement extends RenderedElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'symbol';\n  }\n\n  render(_) {// NO RENDER\n  }\n\n}\n\nclass SVGFontLoader {\n  constructor(document) {\n    this.document = document;\n    this.loaded = false;\n    document.fonts.push(this);\n  }\n\n  load(fontFamily, url) {\n    var _this = this;\n\n    return _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* () {\n      try {\n        var {\n          document\n        } = _this;\n        var svgDocument = yield document.canvg.parser.load(url);\n        var fonts = svgDocument.getElementsByTagName('font');\n        Array.from(fonts).forEach(fontNode => {\n          var font = document.createElement(fontNode);\n          document.definitions[fontFamily] = font;\n        });\n      } catch (err) {\n        console.error(\"Error while loading font \\\"\".concat(url, \"\\\":\"), err);\n      }\n\n      _this.loaded = true;\n    })();\n  }\n\n}\n\nclass StyleElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'style';\n    var css = compressSpaces(Array.from(node.childNodes) // NEED TEST\n    .map(_ => _.textContent).join('').replace(/(\\/\\*([^*]|[\\r\\n]|(\\*+([^*/]|[\\r\\n])))*\\*+\\/)|(^[\\s]*\\/\\/.*)/gm, '') // remove comments\n    .replace(/@import.*;/g, '') // remove imports\n    );\n    var cssDefs = css.split('}');\n    cssDefs.forEach(_ => {\n      var def = _.trim();\n\n      if (!def) {\n        return;\n      }\n\n      var cssParts = def.split('{');\n      var cssClasses = cssParts[0].split(',');\n      var cssProps = cssParts[1].split(';');\n      cssClasses.forEach(_ => {\n        var cssClass = _.trim();\n\n        if (!cssClass) {\n          return;\n        }\n\n        var props = document.styles[cssClass] || {};\n        cssProps.forEach(cssProp => {\n          var prop = cssProp.indexOf(':');\n          var name = cssProp.substr(0, prop).trim();\n          var value = cssProp.substr(prop + 1, cssProp.length - prop).trim();\n\n          if (name && value) {\n            props[name] = new Property(document, name, value);\n          }\n        });\n        document.styles[cssClass] = props;\n        document.stylesSpecificity[cssClass] = getSelectorSpecificity(cssClass);\n\n        if (cssClass === '@font-face') {\n          //  && !nodeEnv\n          var fontFamily = props['font-family'].getString().replace(/\"|'/g, '');\n          var srcs = props.src.getString().split(',');\n          srcs.forEach(src => {\n            if (src.indexOf('format(\"svg\")') > 0) {\n              var url = parseExternalUrl(src);\n\n              if (url) {\n                void new SVGFontLoader(document).load(fontFamily, url);\n              }\n            }\n          });\n        }\n      });\n    });\n  }\n\n}\nStyleElement.parseExternalUrl = parseExternalUrl;\n\nclass UseElement extends RenderedElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'use';\n  }\n\n  setContext(ctx) {\n    super.setContext(ctx);\n    var xAttr = this.getAttribute('x');\n    var yAttr = this.getAttribute('y');\n\n    if (xAttr.hasValue()) {\n      ctx.translate(xAttr.getPixels('x'), 0);\n    }\n\n    if (yAttr.hasValue()) {\n      ctx.translate(0, yAttr.getPixels('y'));\n    }\n  }\n\n  path(ctx) {\n    var {\n      element\n    } = this;\n\n    if (element) {\n      element.path(ctx);\n    }\n  }\n\n  renderChildren(ctx) {\n    var {\n      document,\n      element\n    } = this;\n\n    if (element) {\n      var tempSvg = element;\n\n      if (element.type === 'symbol') {\n        // render me using a temporary svg element in symbol cases (http://www.w3.org/TR/SVG/struct.html#UseElement)\n        tempSvg = new SVGElement(document, null);\n        tempSvg.attributes.viewBox = new Property(document, 'viewBox', element.getAttribute('viewBox').getString());\n        tempSvg.attributes.preserveAspectRatio = new Property(document, 'preserveAspectRatio', element.getAttribute('preserveAspectRatio').getString());\n        tempSvg.attributes.overflow = new Property(document, 'overflow', element.getAttribute('overflow').getString());\n        tempSvg.children = element.children; // element is still the parent of the children\n\n        element.styles.opacity = new Property(document, 'opacity', this.calculateOpacity());\n      }\n\n      if (tempSvg.type === 'svg') {\n        var widthStyle = this.getStyle('width', false, true);\n        var heightStyle = this.getStyle('height', false, true); // if symbol or svg, inherit width/height from me\n\n        if (widthStyle.hasValue()) {\n          tempSvg.attributes.width = new Property(document, 'width', widthStyle.getString());\n        }\n\n        if (heightStyle.hasValue()) {\n          tempSvg.attributes.height = new Property(document, 'height', heightStyle.getString());\n        }\n      }\n\n      var oldParent = tempSvg.parent;\n      tempSvg.parent = this;\n      tempSvg.render(ctx);\n      tempSvg.parent = oldParent;\n    }\n  }\n\n  getBoundingBox(ctx) {\n    var {\n      element\n    } = this;\n\n    if (element) {\n      return element.getBoundingBox(ctx);\n    }\n\n    return null;\n  }\n\n  elementTransform() {\n    var {\n      document,\n      element\n    } = this;\n    return Transform.fromElement(document, element);\n  }\n\n  get element() {\n    if (!this.cachedElement) {\n      this.cachedElement = this.getHrefAttribute().getDefinition();\n    }\n\n    return this.cachedElement;\n  }\n\n}\n\nfunction imGet(img, x, y, width, _height, rgba) {\n  return img[y * width * 4 + x * 4 + rgba];\n}\n\nfunction imSet(img, x, y, width, _height, rgba, val) {\n  img[y * width * 4 + x * 4 + rgba] = val;\n}\n\nfunction m(matrix, i, v) {\n  var mi = matrix[i];\n  return mi * v;\n}\n\nfunction c(a, m1, m2, m3) {\n  return m1 + Math.cos(a) * m2 + Math.sin(a) * m3;\n}\n\nclass FeColorMatrixElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'feColorMatrix';\n    var matrix = toNumbers(this.getAttribute('values').getString());\n\n    switch (this.getAttribute('type').getString('matrix')) {\n      // http://www.w3.org/TR/SVG/filters.html#feColorMatrixElement\n      case 'saturate':\n        {\n          var s = matrix[0];\n          /* eslint-disable array-element-newline */\n\n          matrix = [0.213 + 0.787 * s, 0.715 - 0.715 * s, 0.072 - 0.072 * s, 0, 0, 0.213 - 0.213 * s, 0.715 + 0.285 * s, 0.072 - 0.072 * s, 0, 0, 0.213 - 0.213 * s, 0.715 - 0.715 * s, 0.072 + 0.928 * s, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1];\n          /* eslint-enable array-element-newline */\n\n          break;\n        }\n\n      case 'hueRotate':\n        {\n          var a = matrix[0] * Math.PI / 180.0;\n          /* eslint-disable array-element-newline */\n\n          matrix = [c(a, 0.213, 0.787, -0.213), c(a, 0.715, -0.715, -0.715), c(a, 0.072, -0.072, 0.928), 0, 0, c(a, 0.213, -0.213, 0.143), c(a, 0.715, 0.285, 0.140), c(a, 0.072, -0.072, -0.283), 0, 0, c(a, 0.213, -0.213, -0.787), c(a, 0.715, -0.715, 0.715), c(a, 0.072, 0.928, 0.072), 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1];\n          /* eslint-enable array-element-newline */\n\n          break;\n        }\n\n      case 'luminanceToAlpha':\n        /* eslint-disable array-element-newline */\n        matrix = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.2125, 0.7154, 0.0721, 0, 0, 0, 0, 0, 0, 1];\n        /* eslint-enable array-element-newline */\n\n        break;\n    }\n\n    this.matrix = matrix;\n    this.includeOpacity = this.getAttribute('includeOpacity').hasValue();\n  }\n\n  apply(ctx, _x, _y, width, height) {\n    // assuming x==0 && y==0 for now\n    var {\n      includeOpacity,\n      matrix\n    } = this;\n    var srcData = ctx.getImageData(0, 0, width, height);\n\n    for (var y = 0; y < height; y++) {\n      for (var x = 0; x < width; x++) {\n        var r = imGet(srcData.data, x, y, width, height, 0);\n        var g = imGet(srcData.data, x, y, width, height, 1);\n        var b = imGet(srcData.data, x, y, width, height, 2);\n        var a = imGet(srcData.data, x, y, width, height, 3);\n        var nr = m(matrix, 0, r) + m(matrix, 1, g) + m(matrix, 2, b) + m(matrix, 3, a) + m(matrix, 4, 1);\n        var ng = m(matrix, 5, r) + m(matrix, 6, g) + m(matrix, 7, b) + m(matrix, 8, a) + m(matrix, 9, 1);\n        var nb = m(matrix, 10, r) + m(matrix, 11, g) + m(matrix, 12, b) + m(matrix, 13, a) + m(matrix, 14, 1);\n        var na = m(matrix, 15, r) + m(matrix, 16, g) + m(matrix, 17, b) + m(matrix, 18, a) + m(matrix, 19, 1);\n\n        if (includeOpacity) {\n          nr = 0;\n          ng = 0;\n          nb = 0;\n          na *= a / 255;\n        }\n\n        imSet(srcData.data, x, y, width, height, 0, nr);\n        imSet(srcData.data, x, y, width, height, 1, ng);\n        imSet(srcData.data, x, y, width, height, 2, nb);\n        imSet(srcData.data, x, y, width, height, 3, na);\n      }\n    }\n\n    ctx.clearRect(0, 0, width, height);\n    ctx.putImageData(srcData, 0, 0);\n  }\n\n}\n\nclass MaskElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'mask';\n  }\n\n  apply(ctx, element) {\n    var {\n      document\n    } = this; // render as temp svg\n\n    var x = this.getAttribute('x').getPixels('x');\n    var y = this.getAttribute('y').getPixels('y');\n    var width = this.getStyle('width').getPixels('x');\n    var height = this.getStyle('height').getPixels('y');\n\n    if (!width && !height) {\n      var boundingBox = new BoundingBox();\n      this.children.forEach(child => {\n        boundingBox.addBoundingBox(child.getBoundingBox(ctx));\n      });\n      x = Math.floor(boundingBox.x1);\n      y = Math.floor(boundingBox.y1);\n      width = Math.floor(boundingBox.width);\n      height = Math.floor(boundingBox.height);\n    }\n\n    var ignoredStyles = this.removeStyles(element, MaskElement.ignoreStyles);\n    var maskCanvas = document.createCanvas(x + width, y + height);\n    var maskCtx = maskCanvas.getContext('2d');\n    document.screen.setDefaults(maskCtx);\n    this.renderChildren(maskCtx); // convert mask to alpha with a fake node\n    // TODO: refactor out apply from feColorMatrix\n\n    new FeColorMatrixElement(document, {\n      nodeType: 1,\n      childNodes: [],\n      attributes: [{\n        nodeName: 'type',\n        value: 'luminanceToAlpha'\n      }, {\n        nodeName: 'includeOpacity',\n        value: 'true'\n      }]\n    }).apply(maskCtx, 0, 0, x + width, y + height);\n    var tmpCanvas = document.createCanvas(x + width, y + height);\n    var tmpCtx = tmpCanvas.getContext('2d');\n    document.screen.setDefaults(tmpCtx);\n    element.render(tmpCtx);\n    tmpCtx.globalCompositeOperation = 'destination-in';\n    tmpCtx.fillStyle = maskCtx.createPattern(maskCanvas, 'no-repeat');\n    tmpCtx.fillRect(0, 0, x + width, y + height);\n    ctx.fillStyle = tmpCtx.createPattern(tmpCanvas, 'no-repeat');\n    ctx.fillRect(0, 0, x + width, y + height); // reassign mask\n\n    this.restoreStyles(element, ignoredStyles);\n  }\n\n  render(_) {// NO RENDER\n  }\n\n}\nMaskElement.ignoreStyles = ['mask', 'transform', 'clip-path'];\n\nvar noop = () => {// NOOP\n};\n\nclass ClipPathElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'clipPath';\n  }\n\n  apply(ctx) {\n    var {\n      document\n    } = this;\n    var contextProto = Reflect.getPrototypeOf(ctx);\n    var {\n      beginPath,\n      closePath\n    } = ctx;\n\n    if (contextProto) {\n      contextProto.beginPath = noop;\n      contextProto.closePath = noop;\n    }\n\n    Reflect.apply(beginPath, ctx, []);\n    this.children.forEach(child => {\n      if (typeof child.path === 'undefined') {\n        return;\n      }\n\n      var transform = typeof child.elementTransform !== 'undefined' ? child.elementTransform() : null; // handle <use />\n\n      if (!transform) {\n        transform = Transform.fromElement(document, child);\n      }\n\n      if (transform) {\n        transform.apply(ctx);\n      }\n\n      child.path(ctx);\n\n      if (contextProto) {\n        contextProto.closePath = closePath;\n      }\n\n      if (transform) {\n        transform.unapply(ctx);\n      }\n    });\n    Reflect.apply(closePath, ctx, []);\n    ctx.clip();\n\n    if (contextProto) {\n      contextProto.beginPath = beginPath;\n      contextProto.closePath = closePath;\n    }\n  }\n\n  render(_) {// NO RENDER\n  }\n\n}\n\nclass FilterElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'filter';\n  }\n\n  apply(ctx, element) {\n    // render as temp svg\n    var {\n      document,\n      children\n    } = this;\n    var boundingBox = element.getBoundingBox(ctx);\n\n    if (!boundingBox) {\n      return;\n    }\n\n    var px = 0;\n    var py = 0;\n    children.forEach(child => {\n      var efd = child.extraFilterDistance || 0;\n      px = Math.max(px, efd);\n      py = Math.max(py, efd);\n    });\n    var width = Math.floor(boundingBox.width);\n    var height = Math.floor(boundingBox.height);\n    var tmpCanvasWidth = width + 2 * px;\n    var tmpCanvasHeight = height + 2 * py;\n\n    if (tmpCanvasWidth < 1 || tmpCanvasHeight < 1) {\n      return;\n    }\n\n    var x = Math.floor(boundingBox.x);\n    var y = Math.floor(boundingBox.y);\n    var ignoredStyles = this.removeStyles(element, FilterElement.ignoreStyles);\n    var tmpCanvas = document.createCanvas(tmpCanvasWidth, tmpCanvasHeight);\n    var tmpCtx = tmpCanvas.getContext('2d');\n    document.screen.setDefaults(tmpCtx);\n    tmpCtx.translate(-x + px, -y + py);\n    element.render(tmpCtx); // apply filters\n\n    children.forEach(child => {\n      if (typeof child.apply === 'function') {\n        child.apply(tmpCtx, 0, 0, tmpCanvasWidth, tmpCanvasHeight);\n      }\n    }); // render on me\n\n    ctx.drawImage(tmpCanvas, 0, 0, tmpCanvasWidth, tmpCanvasHeight, x - px, y - py, tmpCanvasWidth, tmpCanvasHeight);\n    this.restoreStyles(element, ignoredStyles);\n  }\n\n  render(_) {// NO RENDER\n  }\n\n}\nFilterElement.ignoreStyles = ['filter', 'transform', 'clip-path'];\n\nclass FeDropShadowElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'feDropShadow';\n    this.addStylesFromStyleDefinition();\n  }\n\n  apply(_, _x, _y, _width, _height) {// TODO: implement\n  }\n\n}\n\nclass FeMorphologyElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'feMorphology';\n  }\n\n  apply(_, _x, _y, _width, _height) {// TODO: implement\n  }\n\n}\n\nclass FeCompositeElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'feComposite';\n  }\n\n  apply(_, _x, _y, _width, _height) {// TODO: implement\n  }\n\n}\n\nclass FeGaussianBlurElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'feGaussianBlur';\n    this.blurRadius = Math.floor(this.getAttribute('stdDeviation').getNumber());\n    this.extraFilterDistance = this.blurRadius;\n  }\n\n  apply(ctx, x, y, width, height) {\n    var {\n      document,\n      blurRadius\n    } = this;\n    var body = document.window ? document.window.document.body : null;\n    var canvas = ctx.canvas; // StackBlur requires canvas be on document\n\n    canvas.id = document.getUniqueId();\n\n    if (body) {\n      canvas.style.display = 'none';\n      body.appendChild(canvas);\n    }\n\n    (0,stackblur_canvas__WEBPACK_IMPORTED_MODULE_19__.canvasRGBA)(canvas, x, y, width, height, blurRadius);\n\n    if (body) {\n      body.removeChild(canvas);\n    }\n  }\n\n}\n\nclass TitleElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'title';\n  }\n\n}\n\nclass DescElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'desc';\n  }\n\n}\n\nvar elements = {\n  'svg': SVGElement,\n  'rect': RectElement,\n  'circle': CircleElement,\n  'ellipse': EllipseElement,\n  'line': LineElement,\n  'polyline': PolylineElement,\n  'polygon': PolygonElement,\n  'path': PathElement,\n  'pattern': PatternElement,\n  'marker': MarkerElement,\n  'defs': DefsElement,\n  'linearGradient': LinearGradientElement,\n  'radialGradient': RadialGradientElement,\n  'stop': StopElement,\n  'animate': AnimateElement,\n  'animateColor': AnimateColorElement,\n  'animateTransform': AnimateTransformElement,\n  'font': FontElement,\n  'font-face': FontFaceElement,\n  'missing-glyph': MissingGlyphElement,\n  'glyph': GlyphElement,\n  'text': TextElement,\n  'tspan': TSpanElement,\n  'tref': TRefElement,\n  'a': AElement,\n  'textPath': TextPathElement,\n  'image': ImageElement,\n  'g': GElement,\n  'symbol': SymbolElement,\n  'style': StyleElement,\n  'use': UseElement,\n  'mask': MaskElement,\n  'clipPath': ClipPathElement,\n  'filter': FilterElement,\n  'feDropShadow': FeDropShadowElement,\n  'feMorphology': FeMorphologyElement,\n  'feComposite': FeCompositeElement,\n  'feColorMatrix': FeColorMatrixElement,\n  'feGaussianBlur': FeGaussianBlurElement,\n  'title': TitleElement,\n  'desc': DescElement\n};\n\nfunction ownKeys$1(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread$1(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys$1(Object(source), true).forEach(function (key) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_7__(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys$1(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction createCanvas(width, height) {\n  var canvas = document.createElement('canvas');\n  canvas.width = width;\n  canvas.height = height;\n  return canvas;\n}\n\nfunction createImage(_x) {\n  return _createImage.apply(this, arguments);\n}\n\nfunction _createImage() {\n  _createImage = _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* (src) {\n    var anonymousCrossOrigin = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var image = document.createElement('img');\n\n    if (anonymousCrossOrigin) {\n      image.crossOrigin = 'Anonymous';\n    }\n\n    return new Promise((resolve, reject) => {\n      image.onload = () => {\n        resolve(image);\n      };\n\n      image.onerror = (_event, _source, _lineno, _colno, error) => {\n        reject(error);\n      };\n\n      image.src = src;\n    });\n  });\n  return _createImage.apply(this, arguments);\n}\n\nclass Document {\n  constructor(canvg) {\n    var {\n      rootEmSize = 12,\n      emSize = 12,\n      createCanvas = Document.createCanvas,\n      createImage = Document.createImage,\n      anonymousCrossOrigin\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.canvg = canvg;\n    this.definitions = Object.create(null);\n    this.styles = Object.create(null);\n    this.stylesSpecificity = Object.create(null);\n    this.images = [];\n    this.fonts = [];\n    this.emSizeStack = [];\n    this.uniqueId = 0;\n    this.screen = canvg.screen;\n    this.rootEmSize = rootEmSize;\n    this.emSize = emSize;\n    this.createCanvas = createCanvas;\n    this.createImage = this.bindCreateImage(createImage, anonymousCrossOrigin);\n    this.screen.wait(this.isImagesLoaded.bind(this));\n    this.screen.wait(this.isFontsLoaded.bind(this));\n  }\n\n  bindCreateImage(createImage, anonymousCrossOrigin) {\n    if (typeof anonymousCrossOrigin === 'boolean') {\n      return (source, forceAnonymousCrossOrigin) => createImage(source, typeof forceAnonymousCrossOrigin === 'boolean' ? forceAnonymousCrossOrigin : anonymousCrossOrigin);\n    }\n\n    return createImage;\n  }\n\n  get window() {\n    return this.screen.window;\n  }\n\n  get fetch() {\n    return this.screen.fetch;\n  }\n\n  get ctx() {\n    return this.screen.ctx;\n  }\n\n  get emSize() {\n    var {\n      emSizeStack\n    } = this;\n    return emSizeStack[emSizeStack.length - 1];\n  }\n\n  set emSize(value) {\n    var {\n      emSizeStack\n    } = this;\n    emSizeStack.push(value);\n  }\n\n  popEmSize() {\n    var {\n      emSizeStack\n    } = this;\n    emSizeStack.pop();\n  }\n\n  getUniqueId() {\n    return \"canvg\".concat(++this.uniqueId);\n  }\n\n  isImagesLoaded() {\n    return this.images.every(_ => _.loaded);\n  }\n\n  isFontsLoaded() {\n    return this.fonts.every(_ => _.loaded);\n  }\n\n  createDocumentElement(document) {\n    var documentElement = this.createElement(document.documentElement);\n    documentElement.root = true;\n    documentElement.addStylesFromStyleDefinition();\n    this.documentElement = documentElement;\n    return documentElement;\n  }\n\n  createElement(node) {\n    var elementType = node.nodeName.replace(/^[^:]+:/, '');\n    var ElementType = Document.elementTypes[elementType];\n\n    if (typeof ElementType !== 'undefined') {\n      return new ElementType(this, node);\n    }\n\n    return new UnknownElement(this, node);\n  }\n\n  createTextNode(node) {\n    return new TextNode(this, node);\n  }\n\n  setViewBox(config) {\n    this.screen.setViewBox(_objectSpread$1({\n      document: this\n    }, config));\n  }\n\n}\nDocument.createCanvas = createCanvas;\nDocument.createImage = createImage;\nDocument.elementTypes = elements;\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_7__(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n/**\r\n * SVG renderer on canvas.\r\n */\n\nclass Canvg {\n  /**\r\n   * Main constructor.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG Document.\r\n   * @param options - Rendering options.\r\n   */\n  constructor(ctx, svg) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    this.parser = new Parser(options);\n    this.screen = new Screen(ctx, options);\n    this.options = options;\n    var document = new Document(this, options);\n    var documentElement = document.createDocumentElement(svg);\n    this.document = document;\n    this.documentElement = documentElement;\n  }\n  /**\r\n   * Create Canvg instance from SVG source string or URL.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG source string or URL.\r\n   * @param options - Rendering options.\r\n   * @returns Canvg instance.\r\n   */\n\n\n  static from(ctx, svg) {\n    var _arguments = arguments;\n    return _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* () {\n      var options = _arguments.length > 2 && _arguments[2] !== undefined ? _arguments[2] : {};\n      var parser = new Parser(options);\n      var svgDocument = yield parser.parse(svg);\n      return new Canvg(ctx, svgDocument, options);\n    })();\n  }\n  /**\r\n   * Create Canvg instance from SVG source string.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG source string.\r\n   * @param options - Rendering options.\r\n   * @returns Canvg instance.\r\n   */\n\n\n  static fromString(ctx, svg) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var parser = new Parser(options);\n    var svgDocument = parser.parseFromString(svg);\n    return new Canvg(ctx, svgDocument, options);\n  }\n  /**\r\n   * Create new Canvg instance with inherited options.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG source string or URL.\r\n   * @param options - Rendering options.\r\n   * @returns Canvg instance.\r\n   */\n\n\n  fork(ctx, svg) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    return Canvg.from(ctx, svg, _objectSpread(_objectSpread({}, this.options), options));\n  }\n  /**\r\n   * Create new Canvg instance with inherited options.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG source string.\r\n   * @param options - Rendering options.\r\n   * @returns Canvg instance.\r\n   */\n\n\n  forkString(ctx, svg) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    return Canvg.fromString(ctx, svg, _objectSpread(_objectSpread({}, this.options), options));\n  }\n  /**\r\n   * Document is ready promise.\r\n   * @returns Ready promise.\r\n   */\n\n\n  ready() {\n    return this.screen.ready();\n  }\n  /**\r\n   * Document is ready value.\r\n   * @returns Is ready or not.\r\n   */\n\n\n  isReady() {\n    return this.screen.isReady();\n  }\n  /**\r\n   * Render only first frame, ignoring animations and mouse.\r\n   * @param options - Rendering options.\r\n   */\n\n\n  render() {\n    var _arguments2 = arguments,\n        _this = this;\n\n    return _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* () {\n      var options = _arguments2.length > 0 && _arguments2[0] !== undefined ? _arguments2[0] : {};\n\n      _this.start(_objectSpread({\n        enableRedraw: true,\n        ignoreAnimation: true,\n        ignoreMouse: true\n      }, options));\n\n      yield _this.ready();\n\n      _this.stop();\n    })();\n  }\n  /**\r\n   * Start rendering.\r\n   * @param options - Render options.\r\n   */\n\n\n  start() {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var {\n      documentElement,\n      screen,\n      options: baseOptions\n    } = this;\n    screen.start(documentElement, _objectSpread(_objectSpread({\n      enableRedraw: true\n    }, baseOptions), options));\n  }\n  /**\r\n   * Stop rendering.\r\n   */\n\n\n  stop() {\n    this.screen.stop();\n  }\n  /**\r\n   * Resize SVG to fit in given size.\r\n   * @param width\r\n   * @param height\r\n   * @param preserveAspectRatio\r\n   */\n\n\n  resize(width) {\n    var height = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : width;\n    var preserveAspectRatio = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    this.documentElement.resize(width, height, preserveAspectRatio);\n  }\n\n}\n\n\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/canvg/lib/index.es.js\n");

/***/ })

};
;