import Joi from "joi";

const updateUserProfileValidation = Joi.object({
  firstName: Joi.string().required().trim().min(1).max(50).messages({
    "string.empty": "First name cannot be empty",
    "string.min": "First name must be at least 1 character long",
    "string.max": "First name cannot exceed 50 characters",
    "any.required": "First name is required",
  }),
  lastName: Joi.string().required().trim().min(1).max(50).messages({
    "string.empty": "Last name cannot be empty",
    "string.min": "Last name must be at least 1 character long",
    "string.max": "Last name cannot exceed 50 characters",
    "any.required": "Last name is required",
  }),
  image: Joi.string().allow(null),
});

export default updateUserProfileValidation;
