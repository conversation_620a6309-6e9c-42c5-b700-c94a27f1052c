{"version": 3, "sources": ["../../src/index.tsx"], "sourcesContent": ["import React from 'react';\n\ntype AllowedInputTypes = 'password' | 'text' | 'number' | 'tel';\n\ntype InputProps = Required<\n  Pick<\n    React.InputHTMLAttributes<HTMLInputElement>,\n    | 'value'\n    | 'onChange'\n    | 'onFocus'\n    | 'onBlur'\n    | 'onKeyDown'\n    | 'onPaste'\n    | 'aria-label'\n    | 'autoComplete'\n    | 'style'\n    | 'inputMode'\n    | 'onInput'\n  > & {\n    ref: React.RefCallback<HTMLInputElement>;\n    placeholder: string | undefined;\n    className: string | undefined;\n    type: AllowedInputTypes;\n  }\n>;\n\ninterface OTPInputProps {\n  /** Value of the OTP input */\n  value?: string;\n  /** Number of OTP inputs to be rendered */\n  numInputs?: number;\n  /** Callback to be called when the OTP value changes */\n  onChange: (otp: string) => void;\n  /** Callback to be called when pasting content into the component */\n  onPaste?: (event: React.ClipboardEvent<HTMLDivElement>) => void;\n  /** Function to render the input */\n  renderInput: (inputProps: InputProps, index: number) => React.ReactNode;\n  /** Whether the first input should be auto focused */\n  shouldAutoFocus?: boolean;\n  /** Placeholder for the inputs */\n  placeholder?: string;\n  /** Function to render the separator */\n  renderSeparator?: ((index: number) => React.ReactNode) | React.ReactNode;\n  /** Style for the container */\n  containerStyle?: React.CSSProperties | string;\n  /** Style for the input */\n  inputStyle?: React.CSSProperties | string;\n  /** The type that will be passed to the input being rendered */\n  inputType?: AllowedInputTypes;\n  /** Do not apply the default styles to the inputs, will be removed in future versions */\n  skipDefaultStyles?: boolean; // TODO: Remove in next major release\n}\n\nconst isStyleObject = (obj: unknown) => typeof obj === 'object' && obj !== null;\n\nconst OTPInput = ({\n  value = '',\n  numInputs = 4,\n  onChange,\n  onPaste,\n  renderInput,\n  shouldAutoFocus = false,\n  inputType = 'text',\n  renderSeparator,\n  placeholder,\n  containerStyle,\n  inputStyle,\n  skipDefaultStyles = false,\n}: OTPInputProps) => {\n  const [activeInput, setActiveInput] = React.useState(0);\n  const inputRefs = React.useRef<Array<HTMLInputElement | null>>([]);\n\n  const getOTPValue = () => (value ? value.toString().split('') : []);\n\n  const isInputNum = inputType === 'number' || inputType === 'tel';\n\n  React.useEffect(() => {\n    inputRefs.current = inputRefs.current.slice(0, numInputs);\n  }, [numInputs]);\n\n  React.useEffect(() => {\n    if (shouldAutoFocus) {\n      inputRefs.current[0]?.focus();\n    }\n  }, [shouldAutoFocus]);\n\n  const getPlaceholderValue = () => {\n    if (typeof placeholder === 'string') {\n      if (placeholder.length === numInputs) {\n        return placeholder;\n      }\n\n      if (placeholder.length > 0) {\n        console.error('Length of the placeholder should be equal to the number of inputs.');\n      }\n    }\n    return undefined;\n  };\n\n  const isInputValueValid = (value: string) => {\n    const isTypeValid = isInputNum ? !isNaN(Number(value)) : typeof value === 'string';\n    return isTypeValid && value.trim().length === 1;\n  };\n\n  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const { value } = event.target;\n\n    if (isInputValueValid(value)) {\n      changeCodeAtFocus(value);\n      focusInput(activeInput + 1);\n    }\n  };\n\n  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const { nativeEvent } = event;\n    const value = event.target.value;\n\n    if (!isInputValueValid(value)) {\n      // Pasting from the native autofill suggestion on a mobile device can pass\n      // the pasted string as one long input to one of the cells. This ensures\n      // that we handle the full input and not just the first character.\n      if (value.length === numInputs) {\n        const hasInvalidInput = value.split('').some((cellInput) => !isInputValueValid(cellInput));\n        if (!hasInvalidInput) {\n          handleOTPChange(value.split(''));\n          focusInput(numInputs - 1);\n        }\n      }\n\n      // @ts-expect-error - This was added previously to handle and edge case\n      // for dealing with keyCode \"229 Unidentified\" on Android. Check if this is\n      // still needed.\n      if (nativeEvent.data === null && nativeEvent.inputType === 'deleteContentBackward') {\n        event.preventDefault();\n        changeCodeAtFocus('');\n        focusInput(activeInput - 1);\n      }\n\n      // Clear the input if it's not valid value because firefox allows\n      // pasting non-numeric characters in a number type input\n      event.target.value = '';\n    }\n  };\n\n  const handleFocus = (event: React.FocusEvent<HTMLInputElement>) => (index: number) => {\n    setActiveInput(index);\n    event.target.select();\n  };\n\n  const handleBlur = () => {\n    setActiveInput(activeInput - 1);\n  };\n\n  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {\n    const otp = getOTPValue();\n    if ([event.code, event.key].includes('Backspace')) {\n      event.preventDefault();\n      changeCodeAtFocus('');\n      focusInput(activeInput - 1);\n    } else if (event.code === 'Delete') {\n      event.preventDefault();\n      changeCodeAtFocus('');\n    } else if (event.code === 'ArrowLeft') {\n      event.preventDefault();\n      focusInput(activeInput - 1);\n    } else if (event.code === 'ArrowRight') {\n      event.preventDefault();\n      focusInput(activeInput + 1);\n    }\n    // React does not trigger onChange when the same value is entered\n    // again. So we need to focus the next input manually in this case.\n    else if (event.key === otp[activeInput]) {\n      event.preventDefault();\n      focusInput(activeInput + 1);\n    } else if (\n      event.code === 'Spacebar' ||\n      event.code === 'Space' ||\n      event.code === 'ArrowUp' ||\n      event.code === 'ArrowDown'\n    ) {\n      event.preventDefault();\n    }\n  };\n\n  const focusInput = (index: number) => {\n    const activeInput = Math.max(Math.min(numInputs - 1, index), 0);\n\n    if (inputRefs.current[activeInput]) {\n      inputRefs.current[activeInput]?.focus();\n      inputRefs.current[activeInput]?.select();\n      setActiveInput(activeInput);\n    }\n  };\n\n  const changeCodeAtFocus = (value: string) => {\n    const otp = getOTPValue();\n    otp[activeInput] = value[0];\n    handleOTPChange(otp);\n  };\n\n  const handleOTPChange = (otp: Array<string>) => {\n    const otpValue = otp.join('');\n    onChange(otpValue);\n  };\n\n  const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>) => {\n    event.preventDefault();\n\n    const otp = getOTPValue();\n    let nextActiveInput = activeInput;\n\n    // Get pastedData in an array of max size (num of inputs - current position)\n    const pastedData = event.clipboardData\n      .getData('text/plain')\n      .slice(0, numInputs - activeInput)\n      .split('');\n\n    // Prevent pasting if the clipboard data contains non-numeric values for number inputs\n    if (isInputNum && pastedData.some((value) => isNaN(Number(value)))) {\n      return;\n    }\n\n    // Paste data from focused input onwards\n    for (let pos = 0; pos < numInputs; ++pos) {\n      if (pos >= activeInput && pastedData.length > 0) {\n        otp[pos] = pastedData.shift() ?? '';\n        nextActiveInput++;\n      }\n    }\n\n    focusInput(nextActiveInput);\n    handleOTPChange(otp);\n  };\n\n  return (\n    <div\n      style={Object.assign({ display: 'flex', alignItems: 'center' }, isStyleObject(containerStyle) && containerStyle)}\n      className={typeof containerStyle === 'string' ? containerStyle : undefined}\n      onPaste={onPaste}\n    >\n      {Array.from({ length: numInputs }, (_, index) => index).map((index) => (\n        <React.Fragment key={index}>\n          {renderInput(\n            {\n              value: getOTPValue()[index] ?? '',\n              placeholder: getPlaceholderValue()?.[index] ?? undefined,\n              ref: (element) => (inputRefs.current[index] = element),\n              onChange: handleChange,\n              onFocus: (event) => handleFocus(event)(index),\n              onBlur: handleBlur,\n              onKeyDown: handleKeyDown,\n              onPaste: handlePaste,\n              autoComplete: 'off',\n              'aria-label': `Please enter OTP character ${index + 1}`,\n              style: Object.assign(\n                !skipDefaultStyles ? ({ width: '1em', textAlign: 'center' } as const) : {},\n                isStyleObject(inputStyle) ? inputStyle : {}\n              ),\n              className: typeof inputStyle === 'string' ? inputStyle : undefined,\n              type: inputType,\n              inputMode: isInputNum ? 'numeric' : 'text',\n              onInput: handleInputChange,\n            },\n            index\n          )}\n          {index < numInputs - 1 && (typeof renderSeparator === 'function' ? renderSeparator(index) : renderSeparator)}\n        </React.Fragment>\n      ))}\n    </div>\n  );\n};\n\nexport type { OTPInputProps, InputProps, AllowedInputTypes };\nexport default OTPInput;\n"], "mappings": ";;;;;;;;;AAqDA,IAAM,gBAAgB,SAAC,KAAY;AAAK,SAAA,OAAO,QAAQ,YAAY,QAAQ;AAAI;AAEzE,IAAA,WAAW,SAAC,IAaF;MAZd,KAAU,GAAA,OAAV,QAAK,OAAA,SAAG,KAAE,IACV,KAAa,GAAA,WAAb,YAAS,OAAA,SAAG,IAAC,IACb,WAAQ,GAAA,UACR,UAAO,GAAA,SACP,cAAW,GAAA,aACX,KAAuB,GAAA,iBAAvB,kBAAe,OAAA,SAAG,QAAK,IACvB,KAAkB,GAAA,WAAlB,YAAS,OAAA,SAAG,SAAM,IAClB,kBAAe,GAAA,iBACf,cAAW,GAAA,aACX,iBAAc,GAAA,gBACd,aAAU,GAAA,YACV,KAAA,GAAA,mBAAA,oBAAoB,OAAA,SAAA,QAAK;AAEnB,MAAA,KAAgC,aAAAA,QAAM,SAAS,CAAC,GAA/C,cAAW,GAAA,CAAA,GAAE,iBAAc,GAAA,CAAA;AAClC,MAAM,YAAY,aAAAA,QAAM,OAAuC,CAAA,CAAE;AAEjE,MAAM,cAAc,WAAA;AAAM,WAAC,QAAQ,MAAM,SAAQ,EAAG,MAAM,EAAE,IAAI,CAAA;EAAtC;AAE1B,MAAM,aAAa,cAAc,YAAY,cAAc;AAE3D,eAAAA,QAAM,UAAU,WAAA;AACd,cAAU,UAAU,UAAU,QAAQ,MAAM,GAAG,SAAS;EAC1D,GAAG,CAAC,SAAS,CAAC;AAEd,eAAAA,QAAM,UAAU,WAAA;;AACd,QAAI,iBAAiB;AACnB,OAAAC,MAAA,UAAU,QAAQ,CAAC,OAAC,QAAAA,QAAA,SAAA,SAAAA,IAAE,MAAK;IAC5B;EACH,GAAG,CAAC,eAAe,CAAC;AAEpB,MAAM,sBAAsB,WAAA;AAC1B,QAAI,OAAO,gBAAgB,UAAU;AACnC,UAAI,YAAY,WAAW,WAAW;AACpC,eAAO;MACR;AAED,UAAI,YAAY,SAAS,GAAG;AAC1B,gBAAQ,MAAM,oEAAoE;MACnF;IACF;AACD,WAAO;EACT;AAEA,MAAM,oBAAoB,SAACC,QAAa;AACtC,QAAM,cAAc,aAAa,CAAC,MAAM,OAAOA,MAAK,CAAC,IAAI,OAAOA,WAAU;AAC1E,WAAO,eAAeA,OAAM,KAAI,EAAG,WAAW;EAChD;AAEA,MAAM,eAAe,SAAC,OAA0C;AACtD,QAAAA,SAAU,MAAM,OAAM;AAE9B,QAAI,kBAAkBA,MAAK,GAAG;AAC5B,wBAAkBA,MAAK;AACvB,iBAAW,cAAc,CAAC;IAC3B;EACH;AAEA,MAAM,oBAAoB,SAAC,OAA0C;AAC3D,QAAA,cAAgB,MAAK;AAC7B,QAAMA,SAAQ,MAAM,OAAO;AAE3B,QAAI,CAAC,kBAAkBA,MAAK,GAAG;AAI7B,UAAIA,OAAM,WAAW,WAAW;AAC9B,YAAM,kBAAkBA,OAAM,MAAM,EAAE,EAAE,KAAK,SAAC,WAAS;AAAK,iBAAA,CAAC,kBAAkB,SAAS;QAAC,CAAA;AACzF,YAAI,CAAC,iBAAiB;AACpB,0BAAgBA,OAAM,MAAM,EAAE,CAAC;AAC/B,qBAAW,YAAY,CAAC;QACzB;MACF;AAKD,UAAI,YAAY,SAAS,QAAQ,YAAY,cAAc,yBAAyB;AAClF,cAAM,eAAc;AACpB,0BAAkB,EAAE;AACpB,mBAAW,cAAc,CAAC;MAC3B;AAID,YAAM,OAAO,QAAQ;IACtB;EACH;AAEA,MAAM,cAAc,SAAC,OAA8C;AAAA,WAAA,SAAC,OAAa;AAC/E,qBAAe,KAAK;AACpB,YAAM,OAAO,OAAM;;EACpB;AAED,MAAM,aAAa,WAAA;AACjB,mBAAe,cAAc,CAAC;EAChC;AAEA,MAAM,gBAAgB,SAAC,OAA4C;AACjE,QAAM,MAAM,YAAW;AACvB,QAAI,CAAC,MAAM,MAAM,MAAM,GAAG,EAAE,SAAS,WAAW,GAAG;AACjD,YAAM,eAAc;AACpB,wBAAkB,EAAE;AACpB,iBAAW,cAAc,CAAC;IAC3B,WAAU,MAAM,SAAS,UAAU;AAClC,YAAM,eAAc;AACpB,wBAAkB,EAAE;IACrB,WAAU,MAAM,SAAS,aAAa;AACrC,YAAM,eAAc;AACpB,iBAAW,cAAc,CAAC;IAC3B,WAAU,MAAM,SAAS,cAAc;AACtC,YAAM,eAAc;AACpB,iBAAW,cAAc,CAAC;IAC3B,WAGQ,MAAM,QAAQ,IAAI,WAAW,GAAG;AACvC,YAAM,eAAc;AACpB,iBAAW,cAAc,CAAC;IAC3B,WACC,MAAM,SAAS,cACf,MAAM,SAAS,WACf,MAAM,SAAS,aACf,MAAM,SAAS,aACf;AACA,YAAM,eAAc;IACrB;EACH;AAEA,MAAM,aAAa,SAAC,OAAa;;AAC/B,QAAMC,eAAc,KAAK,IAAI,KAAK,IAAI,YAAY,GAAG,KAAK,GAAG,CAAC;AAE9D,QAAI,UAAU,QAAQA,YAAW,GAAG;AAClC,OAAAF,MAAA,UAAU,QAAQE,YAAW,OAAC,QAAAF,QAAA,SAAA,SAAAA,IAAE,MAAK;AACrC,OAAAG,MAAA,UAAU,QAAQD,YAAW,OAAC,QAAAC,QAAA,SAAA,SAAAA,IAAE,OAAM;AACtC,qBAAeD,YAAW;IAC3B;EACH;AAEA,MAAM,oBAAoB,SAACD,QAAa;AACtC,QAAM,MAAM,YAAW;AACvB,QAAI,WAAW,IAAIA,OAAM,CAAC;AAC1B,oBAAgB,GAAG;EACrB;AAEA,MAAM,kBAAkB,SAAC,KAAkB;AACzC,QAAM,WAAW,IAAI,KAAK,EAAE;AAC5B,aAAS,QAAQ;EACnB;AAEA,MAAM,cAAc,SAAC,OAA6C;;AAChE,UAAM,eAAc;AAEpB,QAAM,MAAM,YAAW;AACvB,QAAI,kBAAkB;AAGtB,QAAM,aAAa,MAAM,cACtB,QAAQ,YAAY,EACpB,MAAM,GAAG,YAAY,WAAW,EAChC,MAAM,EAAE;AAGX,QAAI,cAAc,WAAW,KAAK,SAACA,QAAU;AAAA,aAAA,MAAM,OAAOA,MAAK,CAAC;IAAnB,CAAoB,GAAG;AAClE;IACD;AAGD,aAAS,MAAM,GAAG,MAAM,WAAW,EAAE,KAAK;AACxC,UAAI,OAAO,eAAe,WAAW,SAAS,GAAG;AAC/C,YAAI,GAAG,KAAID,MAAA,WAAW,MAAK,OAAM,QAAAA,QAAA,SAAAA,MAAA;AACjC;MACD;IACF;AAED,eAAW,eAAe;AAC1B,oBAAgB,GAAG;EACrB;AAEA,SACE,aAAAD,QAAA,cAAA,OAAA,EACE,OAAO,OAAO,OAAO,EAAE,SAAS,QAAQ,YAAY,SAAQ,GAAI,cAAc,cAAc,KAAK,cAAc,GAC/G,WAAW,OAAO,mBAAmB,WAAW,iBAAiB,QACjE,QAAgB,GAEf,MAAM,KAAK,EAAE,QAAQ,UAAS,GAAI,SAAC,GAAG,OAAU;AAAA,WAAA;EAAK,CAAA,EAAE,IAAI,SAAC,OAAK;;AAAK,WACrE,aAAAA,QAAA;MAAC,aAAAA,QAAM;MAAS,EAAA,KAAK,MAAK;MACvB,YACC;QACE,QAAOC,MAAA,YAAW,EAAG,KAAK,OAAC,QAAAA,QAAA,SAAAA,MAAI;QAC/B,cAAaI,OAAAD,MAAA,oBAAmB,OAAK,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAK,OAAC,QAAAC,QAAA,SAAAA,MAAI;QAC/C,KAAK,SAAC,SAAY;AAAA,iBAAC,UAAU,QAAQ,KAAK,IAAI;QAAO;QACrD,UAAU;QACV,SAAS,SAAC,OAAK;AAAK,iBAAA,YAAY,KAAK,EAAE,KAAK;QAAC;QAC7C,QAAQ;QACR,WAAW;QACX,SAAS;QACT,cAAc;QACd,cAAc,8BAAA,OAA8B,QAAQ,CAAC;QACrD,OAAO,OAAO,OACZ,CAAC,oBAAqB,EAAE,OAAO,OAAO,WAAW,SAAQ,IAAe,CAAA,GACxE,cAAc,UAAU,IAAI,aAAa,CAAA,CAAE;QAE7C,WAAW,OAAO,eAAe,WAAW,aAAa;QACzD,MAAM;QACN,WAAW,aAAa,YAAY;QACpC,SAAS;MACV,GACD,KAAK;MAEN,QAAQ,YAAY,MAAM,OAAO,oBAAoB,aAAa,gBAAgB,KAAK,IAAI;IAAgB;GAE/G,CAAC;AAGR;", "names": ["React", "_a", "value", "activeInput", "_b", "_c"]}